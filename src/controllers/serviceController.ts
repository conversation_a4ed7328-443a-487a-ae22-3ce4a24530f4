import { Response } from 'express';
import { body, query, validationResult } from 'express-validator';
import { ServiceService } from '../services/serviceService';
import { asyncHandler } from '../middleware/errorHandler';
import { TenantRequest } from '../middleware/tenantIsolation';

const serviceService = new ServiceService();

export const createServiceValidation = [
  body('customer_id')
    .notEmpty()
    .withMessage('Customer ID is required')
    .isUUID()
    .withMessage('Customer ID must be a valid UUID'),
  body('title')
    .notEmpty()
    .withMessage('Service title is required')
    .isLength({ min: 3, max: 255 })
    .withMessage('Service title must be between 3 and 255 characters'),
  body('service_type')
    .optional()
    .isIn(['normal', 'periodic', 'warranty', 'emergency'])
    .withMessage('Service type must be one of: normal, periodic, warranty, emergency'),
  body('priority')
    .optional()
    .isIn(['low', 'medium', 'high', 'urgent'])
    .withMessage('Priority must be one of: low, medium, high, urgent'),
  body('assigned_to')
    .optional()
    .isUUID()
    .withMessage('Assigned to must be a valid UUID'),
  body('estimated_completion_date')
    .optional()
    .isISO8601()
    .withMessage('Estimated completion date must be a valid date'),
  body('total_cost')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Total cost must be a positive number'),
];

export const updateServiceValidation = [
  body('customer_id')
    .optional()
    .isUUID()
    .withMessage('Customer ID must be a valid UUID'),
  body('title')
    .optional()
    .isLength({ min: 3, max: 255 })
    .withMessage('Service title must be between 3 and 255 characters'),
  body('service_type')
    .optional()
    .isIn(['normal', 'periodic', 'warranty', 'emergency'])
    .withMessage('Service type must be one of: normal, periodic, warranty, emergency'),
  body('status')
    .optional()
    .isIn(['pending', 'in_progress', 'completed', 'cancelled'])
    .withMessage('Status must be one of: pending, in_progress, completed, cancelled'),
  body('priority')
    .optional()
    .isIn(['low', 'medium', 'high', 'urgent'])
    .withMessage('Priority must be one of: low, medium, high, urgent'),
  body('assigned_to')
    .optional()
    .isUUID()
    .withMessage('Assigned to must be a valid UUID'),
  body('estimated_completion_date')
    .optional()
    .isISO8601()
    .withMessage('Estimated completion date must be a valid date'),
  body('completed_at')
    .optional()
    .isISO8601()
    .withMessage('Completed at must be a valid date'),
  body('total_cost')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Total cost must be a positive number'),
];

export const paginationValidation = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),
  query('search')
    .optional()
    .isLength({ min: 1, max: 255 })
    .withMessage('Search term must be between 1 and 255 characters'),
  query('status')
    .optional()
    .isIn(['pending', 'in_progress', 'completed', 'cancelled'])
    .withMessage('Status must be one of: pending, in_progress, completed, cancelled'),
  query('service_type')
    .optional()
    .isIn(['normal', 'periodic', 'warranty', 'emergency'])
    .withMessage('Service type must be one of: normal, periodic, warranty, emergency'),
];

export const getServices = asyncHandler(async (req: TenantRequest, res: Response) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      error: 'Validation failed',
      details: errors.array(),
    });
  }

  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 50;
    const search = req.query.search as string;
    const status = req.query.status as string;
    const serviceType = req.query.service_type as string;

    let result;
    if (search) {
      result = await serviceService.search(req.user!.userId, req.tenantId!, search, page, limit);
    } else {
      result = await serviceService.getAll(req.user!.userId, req.tenantId!, page, limit, status, serviceType);
    }

    res.json({
      success: true,
      data: result,
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      error: error.message,
    });
  }
});

export const getServiceById = asyncHandler(async (req: TenantRequest, res: Response) => {
  try {
    const service = await serviceService.getById(req.params.id, req.user!.userId, req.tenantId!);

    if (!service) {
      return res.status(404).json({
        success: false,
        error: 'Service not found',
      });
    }

    res.json({
      success: true,
      data: service,
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      error: error.message,
    });
  }
});

export const createService = asyncHandler(async (req: TenantRequest, res: Response) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      error: 'Validation failed',
      details: errors.array(),
    });
  }

  try {
    const service = await serviceService.create(req.body, req.user!.userId, req.tenantId!);

    res.status(201).json({
      success: true,
      data: service,
      message: 'Service created successfully',
    });
  } catch (error: any) {
    res.status(400).json({
      success: false,
      error: error.message,
    });
  }
});

export const updateService = asyncHandler(async (req: TenantRequest, res: Response) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      error: 'Validation failed',
      details: errors.array(),
    });
  }

  try {
    const service = await serviceService.update(
      req.params.id,
      req.body,
      req.user!.userId,
      req.tenantId!
    );

    res.json({
      success: true,
      data: service,
      message: 'Service updated successfully',
    });
  } catch (error: any) {
    res.status(400).json({
      success: false,
      error: error.message,
    });
  }
});

export const deleteService = asyncHandler(async (req: TenantRequest, res: Response) => {
  try {
    await serviceService.delete(req.params.id, req.user!.userId, req.tenantId!);

    res.json({
      success: true,
      message: 'Service deleted successfully',
    });
  } catch (error: any) {
    res.status(400).json({
      success: false,
      error: error.message,
    });
  }
});

export const getServicesByStatus = asyncHandler(async (req: TenantRequest, res: Response) => {
  try {
    const services = await serviceService.getServicesByStatus(
      req.user!.userId,
      req.tenantId!,
      req.params.status
    );

    res.json({
      success: true,
      data: services,
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      error: error.message,
    });
  }
});
