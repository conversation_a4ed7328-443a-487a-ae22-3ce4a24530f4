import { Response } from 'express';
import { body, validationResult } from 'express-validator';
import { CustomerService } from '../services/customerService';
import { asyncHandler } from '../middleware/errorHandler';
import { TenantRequest } from '../middleware/tenantIsolation';

const customerService = new CustomerService();

export const createCustomerValidation = [
  body('name').notEmpty().withMessage('Name is required'),
  body('phone').notEmpty().withMessage('Phone is required'),
  body('email').optional().isEmail().withMessage('Valid email is required'),
  body('address').optional().isString(),
];

export const updateCustomerValidation = [
  body('name').optional().notEmpty().withMessage('Name cannot be empty'),
  body('phone').optional().notEmpty().withMessage('Phone cannot be empty'),
  body('email').optional().isEmail().withMessage('Valid email is required'),
  body('address').optional().isString(),
];

export const createCustomer = asyncHandler(async (req: TenantRequest, res: Response) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      error: 'Validation failed',
      details: errors.array(),
    });
  }

  try {
    const customer = await customerService.createCustomer(
      req.body,
      req.tenantId!,
      req.user!.userId
    );

    res.status(201).json({
      success: true,
      data: customer,
      message: 'Customer created successfully',
    });
  } catch (error: any) {
    res.status(400).json({
      success: false,
      error: error.message,
    });
  }
});

export const getCustomers = asyncHandler(async (req: TenantRequest, res: Response) => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const search = req.query.search as string;

    const result = await customerService.getAllCustomers(req.tenantId!, page, limit, search);

    res.json({
      success: true,
      data: result,
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      error: error.message,
    });
  }
});

export const getCustomerById = asyncHandler(async (req: TenantRequest, res: Response) => {
  try {
    const customer = await customerService.getCustomerById(req.params.id, req.tenantId!);

    if (!customer) {
      return res.status(404).json({
        success: false,
        error: 'Customer not found',
      });
    }

    res.json({
      success: true,
      data: customer,
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      error: error.message,
    });
  }
});

export const updateCustomer = asyncHandler(async (req: TenantRequest, res: Response) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      error: 'Validation failed',
      details: errors.array(),
    });
  }

  try {
    const customer = await customerService.updateCustomer(req.params.id, req.body, req.tenantId!);

    res.json({
      success: true,
      data: customer,
      message: 'Customer updated successfully',
    });
  } catch (error: any) {
    res.status(400).json({
      success: false,
      error: error.message,
    });
  }
});

export const deleteCustomer = asyncHandler(async (req: TenantRequest, res: Response) => {
  try {
    await customerService.deleteCustomer(req.params.id, req.tenantId!);

    res.json({
      success: true,
      message: 'Customer deleted successfully',
    });
  } catch (error: any) {
    res.status(400).json({
      success: false,
      error: error.message,
    });
  }
});

export const getCustomerDevices = asyncHandler(async (req: TenantRequest, res: Response) => {
  try {
    const devices = await customerService.getCustomerDevices(req.params.id, req.tenantId!);

    res.json({
      success: true,
      data: devices,
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      error: error.message,
    });
  }
});
