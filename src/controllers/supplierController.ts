import { BaseCrudController } from './baseCrudController';
import { Supplier, CreateSupplierRequest, UpdateSupplierRequest } from '../types/models';
import { SupplierService } from '../services/supplierService';

const supplierService = new SupplierService();

class SupplierController extends BaseCrudController<Supplier, CreateSupplierRequest, UpdateSupplierRequest> {
  constructor() {
    super(supplierService, 'Supplier');
  }
}

const supplierController = new SupplierController();

export const getSuppliers = supplierController.getAll;
export const getSupplierById = supplierController.getById;
export const createSupplier = supplierController.create;
export const updateSupplier = supplierController.update;
export const deleteSupplier = supplierController.delete;

export const createSupplierValidation = supplierController.getCreateValidation();
export const updateSupplierValidation = supplierController.getUpdateValidation();
export const paginationValidation = supplierController.getPaginationValidation();
