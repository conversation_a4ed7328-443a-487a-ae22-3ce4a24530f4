import { Response } from 'express';
import { body, query, validationResult } from 'express-validator';
import { CustomerCategoryService } from '../services/customerCategoryService';
import { asyncHandler } from '../middleware/errorHandler';
import { TenantRequest } from '../middleware/tenantIsolation';

const customerCategoryService = new CustomerCategoryService();

export const createCustomerCategoryValidation = [
  body('name')
    .notEmpty()
    .withMessage('Category name is required')
    .isLength({ min: 2, max: 255 })
    .withMessage('Category name must be between 2 and 255 characters'),
  body('parent_id')
    .optional()
    .isUUID()
    .withMessage('Parent ID must be a valid UUID'),
];

export const updateCustomerCategoryValidation = [
  body('name')
    .optional()
    .isLength({ min: 2, max: 255 })
    .withMessage('Category name must be between 2 and 255 characters'),
  body('parent_id')
    .optional()
    .isUUID()
    .withMessage('Parent ID must be a valid UUID'),
];

export const paginationValidation = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),
  query('search')
    .optional()
    .isLength({ min: 1, max: 255 })
    .withMessage('Search term must be between 1 and 255 characters'),
];

export const getCustomerCategories = asyncHandler(async (req: TenantRequest, res: Response) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      error: 'Validation failed',
      details: errors.array(),
    });
  }

  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 50;
    const search = req.query.search as string;

    let result;
    if (search) {
      result = await customerCategoryService.search(req.user!.userId, req.tenantId!, search, page, limit);
    } else {
      result = await customerCategoryService.getAll(req.user!.userId, req.tenantId!, page, limit);
    }

    res.json({
      success: true,
      data: result,
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      error: error.message,
    });
  }
});

export const getCustomerCategoryHierarchy = asyncHandler(async (req: TenantRequest, res: Response) => {
  try {
    const hierarchy = await customerCategoryService.getHierarchy(req.user!.userId, req.tenantId!);

    res.json({
      success: true,
      data: hierarchy,
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      error: error.message,
    });
  }
});

export const getCustomerCategoryChildren = asyncHandler(async (req: TenantRequest, res: Response) => {
  try {
    const children = await customerCategoryService.getChildren(
      req.params.id,
      req.user!.userId,
      req.tenantId!
    );

    res.json({
      success: true,
      data: children,
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      error: error.message,
    });
  }
});

export const getCustomerCategoryById = asyncHandler(async (req: TenantRequest, res: Response) => {
  try {
    const category = await customerCategoryService.getById(req.params.id, req.user!.userId, req.tenantId!);

    if (!category) {
      return res.status(404).json({
        success: false,
        error: 'Customer category not found',
      });
    }

    res.json({
      success: true,
      data: category,
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      error: error.message,
    });
  }
});

export const createCustomerCategory = asyncHandler(async (req: TenantRequest, res: Response) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      error: 'Validation failed',
      details: errors.array(),
    });
  }

  try {
    const category = await customerCategoryService.create(req.body, req.user!.userId, req.tenantId!);

    res.status(201).json({
      success: true,
      data: category,
      message: 'Customer category created successfully',
    });
  } catch (error: any) {
    res.status(400).json({
      success: false,
      error: error.message,
    });
  }
});

export const updateCustomerCategory = asyncHandler(async (req: TenantRequest, res: Response) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      error: 'Validation failed',
      details: errors.array(),
    });
  }

  try {
    const category = await customerCategoryService.update(
      req.params.id,
      req.body,
      req.user!.userId,
      req.tenantId!
    );

    res.json({
      success: true,
      data: category,
      message: 'Customer category updated successfully',
    });
  } catch (error: any) {
    res.status(400).json({
      success: false,
      error: error.message,
    });
  }
});

export const deleteCustomerCategory = asyncHandler(async (req: TenantRequest, res: Response) => {
  try {
    await customerCategoryService.delete(req.params.id, req.user!.userId, req.tenantId!);

    res.json({
      success: true,
      message: 'Customer category deleted successfully',
    });
  } catch (error: any) {
    res.status(400).json({
      success: false,
      error: error.message,
    });
  }
});
