import { Response } from 'express';
import { query, validationResult } from 'express-validator';
import { DashboardService } from '../services/dashboardService';
import { asyncHandler } from '../middleware/errorHandler';
import { TenantRequest } from '../middleware/tenantIsolation';

const dashboardService = new DashboardService();

export const dashboardValidation = [
  query('days')
    .optional()
    .isInt({ min: 1, max: 365 })
    .withMessage('Days must be between 1 and 365'),
];

export const getDashboardStats = asyncHandler(async (req: TenantRequest, res: Response) => {
  try {
    const stats = await dashboardService.getDashboardStats(req.user!.userId, req.tenantId!);

    res.json({
      success: true,
      data: stats,
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      error: error.message,
    });
  }
});

export const getServiceTrends = asyncHandler(async (req: TenantRequest, res: Response) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      error: 'Validation failed',
      details: errors.array(),
    });
  }

  try {
    const days = parseInt(req.query.days as string) || 30;
    const trends = await dashboardService.getServiceTrends(req.user!.userId, req.tenantId!, days);

    res.json({
      success: true,
      data: trends,
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      error: error.message,
    });
  }
});

export const getCashFlowTrends = asyncHandler(async (req: TenantRequest, res: Response) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      error: 'Validation failed',
      details: errors.array(),
    });
  }

  try {
    const days = parseInt(req.query.days as string) || 30;
    const trends = await dashboardService.getCashFlowTrends(req.user!.userId, req.tenantId!, days);

    res.json({
      success: true,
      data: trends,
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      error: error.message,
    });
  }
});

export const getTopCustomers = asyncHandler(async (req: TenantRequest, res: Response) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      error: 'Validation failed',
      details: errors.array(),
    });
  }

  try {
    const limit = parseInt(req.query.limit as string) || 10;
    const customers = await dashboardService.getTopCustomers(req.user!.userId, req.tenantId!, limit);

    res.json({
      success: true,
      data: customers,
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      error: error.message,
    });
  }
});
