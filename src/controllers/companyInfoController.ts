import { Response } from 'express';
import { body, validationResult } from 'express-validator';
import { CompanyInfoService } from '../services/companyInfoService';
import { asyncHandler } from '../middleware/errorHandler';
import { TenantRequest } from '../middleware/tenantIsolation';

const companyInfoService = new CompanyInfoService();

export const createCompanyInfoValidation = [
  body('company_name')
    .notEmpty()
    .withMessage('Company name is required')
    .isLength({ min: 2, max: 255 })
    .withMessage('Company name must be between 2 and 255 characters'),
  body('phone1')
    .notEmpty()
    .withMessage('Primary phone is required')
    .isLength({ max: 50 })
    .withMessage('Phone number must be less than 50 characters'),
  body('phone2')
    .optional()
    .isLength({ max: 50 })
    .withMessage('Secondary phone must be less than 50 characters'),
  body('city')
    .notEmpty()
    .withMessage('City is required')
    .isLength({ max: 100 })
    .withMessage('City must be less than 100 characters'),
  body('district')
    .notEmpty()
    .withMessage('District is required')
    .isLength({ max: 100 })
    .withMessage('District must be less than 100 characters'),
  body('address')
    .notEmpty()
    .withMessage('Address is required'),
  body('email')
    .optional()
    .isEmail()
    .withMessage('Valid email is required')
    .isLength({ max: 255 })
    .withMessage('Email must be less than 255 characters'),
  body('website')
    .optional()
    .isURL()
    .withMessage('Valid website URL is required')
    .isLength({ max: 255 })
    .withMessage('Website must be less than 255 characters'),
  body('tax_number')
    .optional()
    .isLength({ max: 50 })
    .withMessage('Tax number must be less than 50 characters'),
  body('tax_office')
    .optional()
    .isLength({ max: 100 })
    .withMessage('Tax office must be less than 100 characters'),
];

export const updateCompanyInfoValidation = [
  body('company_name')
    .optional()
    .isLength({ min: 2, max: 255 })
    .withMessage('Company name must be between 2 and 255 characters'),
  body('phone1')
    .optional()
    .isLength({ max: 50 })
    .withMessage('Phone number must be less than 50 characters'),
  body('phone2')
    .optional()
    .isLength({ max: 50 })
    .withMessage('Secondary phone must be less than 50 characters'),
  body('city')
    .optional()
    .isLength({ max: 100 })
    .withMessage('City must be less than 100 characters'),
  body('district')
    .optional()
    .isLength({ max: 100 })
    .withMessage('District must be less than 100 characters'),
  body('address')
    .optional()
    .notEmpty()
    .withMessage('Address cannot be empty'),
  body('email')
    .optional()
    .isEmail()
    .withMessage('Valid email is required')
    .isLength({ max: 255 })
    .withMessage('Email must be less than 255 characters'),
  body('website')
    .optional()
    .isURL()
    .withMessage('Valid website URL is required')
    .isLength({ max: 255 })
    .withMessage('Website must be less than 255 characters'),
  body('tax_number')
    .optional()
    .isLength({ max: 50 })
    .withMessage('Tax number must be less than 50 characters'),
  body('tax_office')
    .optional()
    .isLength({ max: 100 })
    .withMessage('Tax office must be less than 100 characters'),
];

export const getCompanyInfo = asyncHandler(async (req: TenantRequest, res: Response) => {
  try {
    const companyInfo = await companyInfoService.getCompanyInfo(req.user!.userId, req.tenantId!);

    if (!companyInfo) {
      return res.status(404).json({
        success: false,
        error: 'Company info not found',
      });
    }

    res.json({
      success: true,
      data: companyInfo,
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      error: error.message,
    });
  }
});

export const createCompanyInfo = asyncHandler(async (req: TenantRequest, res: Response) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      error: 'Validation failed',
      details: errors.array(),
    });
  }

  try {
    const companyInfo = await companyInfoService.createCompanyInfo(
      req.body,
      req.user!.userId,
      req.tenantId!
    );

    res.status(201).json({
      success: true,
      data: companyInfo,
      message: 'Company info created successfully',
    });
  } catch (error: any) {
    res.status(400).json({
      success: false,
      error: error.message,
    });
  }
});

export const updateCompanyInfo = asyncHandler(async (req: TenantRequest, res: Response) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      error: 'Validation failed',
      details: errors.array(),
    });
  }

  try {
    const companyInfo = await companyInfoService.updateCompanyInfo(
      req.body,
      req.user!.userId,
      req.tenantId!
    );

    res.json({
      success: true,
      data: companyInfo,
      message: 'Company info updated successfully',
    });
  } catch (error: any) {
    res.status(400).json({
      success: false,
      error: error.message,
    });
  }
});

export const deleteCompanyInfo = asyncHandler(async (req: TenantRequest, res: Response) => {
  try {
    await companyInfoService.deleteCompanyInfo(req.user!.userId, req.tenantId!);

    res.json({
      success: true,
      message: 'Company info deleted successfully',
    });
  } catch (error: any) {
    res.status(400).json({
      success: false,
      error: error.message,
    });
  }
});
