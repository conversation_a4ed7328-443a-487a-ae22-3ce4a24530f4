import { Response } from 'express';
import { body, validationResult } from 'express-validator';
import { SmsSettingsService } from '../services/smsSettingsService';
import { asyncHandler } from '../middleware/errorHandler';
import { TenantRequest } from '../middleware/tenantIsolation';

const smsSettingsService = new SmsSettingsService();

export const createSmsSettingsValidation = [
  body('sender_company')
    .notEmpty()
    .withMessage('Sender company is required')
    .isLength({ min: 2, max: 255 })
    .withMessage('Sender company must be between 2 and 255 characters'),
  body('message_header')
    .notEmpty()
    .withMessage('Message header is required')
    .isLength({ min: 1, max: 100 })
    .withMessage('Message header must be between 1 and 100 characters'),
  body('username')
    .notEmpty()
    .withMessage('Username is required')
    .isLength({ min: 1, max: 100 })
    .withMessage('Username must be between 1 and 100 characters'),
  body('password')
    .notEmpty()
    .withMessage('Password is required')
    .isLength({ min: 6 })
    .withMessage('Password must be at least 6 characters'),
  body('sms_template')
    .notEmpty()
    .withMessage('SMS template is required')
    .isLength({ min: 10 })
    .withMessage('SMS template must be at least 10 characters')
    .custom((value) => {
      const requiredFields = ['[[MUSTERIADI]]', '[[MARKAADI]]', '[[TARIH]]'];
      const missingFields = requiredFields.filter(field => !value.includes(field));
      
      if (missingFields.length > 0) {
        throw new Error(`SMS template must contain the following dynamic fields: ${missingFields.join(', ')}`);
      }
      
      return true;
    }),
];

export const updateSmsSettingsValidation = [
  body('sender_company')
    .optional()
    .isLength({ min: 2, max: 255 })
    .withMessage('Sender company must be between 2 and 255 characters'),
  body('message_header')
    .optional()
    .isLength({ min: 1, max: 100 })
    .withMessage('Message header must be between 1 and 100 characters'),
  body('username')
    .optional()
    .isLength({ min: 1, max: 100 })
    .withMessage('Username must be between 1 and 100 characters'),
  body('password')
    .optional()
    .isLength({ min: 6 })
    .withMessage('Password must be at least 6 characters'),
  body('sms_template')
    .optional()
    .isLength({ min: 10 })
    .withMessage('SMS template must be at least 10 characters')
    .custom((value) => {
      if (value) {
        const requiredFields = ['[[MUSTERIADI]]', '[[MARKAADI]]', '[[TARIH]]'];
        const missingFields = requiredFields.filter(field => !value.includes(field));
        
        if (missingFields.length > 0) {
          throw new Error(`SMS template must contain the following dynamic fields: ${missingFields.join(', ')}`);
        }
      }
      
      return true;
    }),
];

export const processSmsTemplateValidation = [
  body('customer_name')
    .notEmpty()
    .withMessage('Customer name is required'),
  body('brand_name')
    .notEmpty()
    .withMessage('Brand name is required'),
  body('date')
    .notEmpty()
    .withMessage('Date is required'),
];

export const getSmsSettings = asyncHandler(async (req: TenantRequest, res: Response) => {
  try {
    const smsSettings = await smsSettingsService.getSmsSettingsSecure(req.user!.userId, req.tenantId!);

    if (!smsSettings) {
      return res.status(404).json({
        success: false,
        error: 'SMS settings not found',
      });
    }

    res.json({
      success: true,
      data: smsSettings,
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      error: error.message,
    });
  }
});

export const createSmsSettings = asyncHandler(async (req: TenantRequest, res: Response) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      error: 'Validation failed',
      details: errors.array(),
    });
  }

  try {
    const smsSettings = await smsSettingsService.createSmsSettings(
      req.body,
      req.user!.userId,
      req.tenantId!
    );

    // Remove password from response
    const { password, ...secureSettings } = smsSettings;

    res.status(201).json({
      success: true,
      data: secureSettings,
      message: 'SMS settings created successfully',
    });
  } catch (error: any) {
    res.status(400).json({
      success: false,
      error: error.message,
    });
  }
});

export const updateSmsSettings = asyncHandler(async (req: TenantRequest, res: Response) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      error: 'Validation failed',
      details: errors.array(),
    });
  }

  try {
    const smsSettings = await smsSettingsService.updateSmsSettings(
      req.body,
      req.user!.userId,
      req.tenantId!
    );

    // Remove password from response
    const { password, ...secureSettings } = smsSettings;

    res.json({
      success: true,
      data: secureSettings,
      message: 'SMS settings updated successfully',
    });
  } catch (error: any) {
    res.status(400).json({
      success: false,
      error: error.message,
    });
  }
});

export const deleteSmsSettings = asyncHandler(async (req: TenantRequest, res: Response) => {
  try {
    await smsSettingsService.deleteSmsSettings(req.user!.userId, req.tenantId!);

    res.json({
      success: true,
      message: 'SMS settings deleted successfully',
    });
  } catch (error: any) {
    res.status(400).json({
      success: false,
      error: error.message,
    });
  }
});

export const processSmsTemplate = asyncHandler(async (req: TenantRequest, res: Response) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      error: 'Validation failed',
      details: errors.array(),
    });
  }

  try {
    const { customer_name, brand_name, date } = req.body;
    
    const processedMessage = await smsSettingsService.processSmsTemplate(
      req.user!.userId,
      req.tenantId!,
      customer_name,
      brand_name,
      date
    );

    res.json({
      success: true,
      data: {
        processed_message: processedMessage,
      },
      message: 'SMS template processed successfully',
    });
  } catch (error: any) {
    res.status(400).json({
      success: false,
      error: error.message,
    });
  }
});
