import { BaseCrudController } from './baseCrudController';
import { PersonnelPosition, CreatePersonnelPositionRequest, UpdatePersonnelPositionRequest } from '../types/models';
import { PersonnelPositionService } from '../services/personnelPositionService';

const personnelPositionService = new PersonnelPositionService();

class PersonnelPositionController extends BaseCrudController<PersonnelPosition, CreatePersonnelPositionRequest, UpdatePersonnelPositionRequest> {
  constructor() {
    super(personnelPositionService, 'Personnel position');
  }
}

const personnelPositionController = new PersonnelPositionController();

export const getPersonnelPositions = personnelPositionController.getAll;
export const getPersonnelPositionById = personnelPositionController.getById;
export const createPersonnelPosition = personnelPositionController.create;
export const updatePersonnelPosition = personnelPositionController.update;
export const deletePersonnelPosition = personnelPositionController.delete;

export const createPersonnelPositionValidation = personnelPositionController.getCreateValidation();
export const updatePersonnelPositionValidation = personnelPositionController.getUpdateValidation();
export const paginationValidation = personnelPositionController.getPaginationValidation();
