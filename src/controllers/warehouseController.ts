import { Response } from 'express';
import { body, query, validationResult } from 'express-validator';
import { WarehouseService } from '../services/warehouseService';
import { asyncHandler } from '../middleware/errorHandler';
import { TenantRequest } from '../middleware/tenantIsolation';

const warehouseService = new WarehouseService();

export const createWarehouseValidation = [
  body('name')
    .notEmpty()
    .withMessage('Warehouse name is required')
    .isLength({ min: 2, max: 255 })
    .withMessage('Warehouse name must be between 2 and 255 characters'),
  body('parent_id')
    .optional()
    .isUUID()
    .withMessage('Parent ID must be a valid UUID'),
];

export const updateWarehouseValidation = [
  body('name')
    .optional()
    .isLength({ min: 2, max: 255 })
    .withMessage('Warehouse name must be between 2 and 255 characters'),
  body('parent_id')
    .optional()
    .isUUID()
    .withMessage('Parent ID must be a valid UUID'),
];

export const paginationValidation = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),
  query('search')
    .optional()
    .isLength({ min: 1, max: 255 })
    .withMessage('Search term must be between 1 and 255 characters'),
];

export const getWarehouses = asyncHandler(async (req: TenantRequest, res: Response) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      error: 'Validation failed',
      details: errors.array(),
    });
  }

  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 50;
    const search = req.query.search as string;

    let result;
    if (search) {
      result = await warehouseService.search(req.user!.userId, req.tenantId!, search, page, limit);
    } else {
      result = await warehouseService.getAll(req.user!.userId, req.tenantId!, page, limit);
    }

    res.json({
      success: true,
      data: result,
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      error: error.message,
    });
  }
});

export const getWarehouseHierarchy = asyncHandler(async (req: TenantRequest, res: Response) => {
  try {
    const hierarchy = await warehouseService.getHierarchy(req.user!.userId, req.tenantId!);

    res.json({
      success: true,
      data: hierarchy,
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      error: error.message,
    });
  }
});

export const getWarehouseChildren = asyncHandler(async (req: TenantRequest, res: Response) => {
  try {
    const children = await warehouseService.getChildren(
      req.params.id,
      req.user!.userId,
      req.tenantId!
    );

    res.json({
      success: true,
      data: children,
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      error: error.message,
    });
  }
});

export const getWarehouseById = asyncHandler(async (req: TenantRequest, res: Response) => {
  try {
    const warehouse = await warehouseService.getById(req.params.id, req.user!.userId, req.tenantId!);

    if (!warehouse) {
      return res.status(404).json({
        success: false,
        error: 'Warehouse not found',
      });
    }

    res.json({
      success: true,
      data: warehouse,
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      error: error.message,
    });
  }
});

export const createWarehouse = asyncHandler(async (req: TenantRequest, res: Response) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      error: 'Validation failed',
      details: errors.array(),
    });
  }

  try {
    const warehouse = await warehouseService.create(req.body, req.user!.userId, req.tenantId!);

    res.status(201).json({
      success: true,
      data: warehouse,
      message: 'Warehouse created successfully',
    });
  } catch (error: any) {
    res.status(400).json({
      success: false,
      error: error.message,
    });
  }
});

export const updateWarehouse = asyncHandler(async (req: TenantRequest, res: Response) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      error: 'Validation failed',
      details: errors.array(),
    });
  }

  try {
    const warehouse = await warehouseService.update(
      req.params.id,
      req.body,
      req.user!.userId,
      req.tenantId!
    );

    res.json({
      success: true,
      data: warehouse,
      message: 'Warehouse updated successfully',
    });
  } catch (error: any) {
    res.status(400).json({
      success: false,
      error: error.message,
    });
  }
});

export const deleteWarehouse = asyncHandler(async (req: TenantRequest, res: Response) => {
  try {
    await warehouseService.delete(req.params.id, req.user!.userId, req.tenantId!);

    res.json({
      success: true,
      message: 'Warehouse deleted successfully',
    });
  } catch (error: any) {
    res.status(400).json({
      success: false,
      error: error.message,
    });
  }
});
