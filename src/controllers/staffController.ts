import { Response } from 'express';
import { body } from 'express-validator';
import { BaseCrudController } from './baseCrudController';
import { Staff, CreateStaffRequest, UpdateStaffRequest } from '../types/models';
import { StaffService } from '../services/staffService';
import { asyncHandler } from '../middleware/errorHandler';
import { TenantRequest } from '../middleware/tenantIsolation';

const staffService = new StaffService();

class StaffController extends BaseCrudController<Staff, CreateStaffRequest, UpdateStaffRequest> {
  constructor() {
    super(staffService, 'Staff member');
  }

  // Override validation for staff-specific fields
  public getCreateValidation() {
    return [
      body('name')
        .notEmpty()
        .withMessage('Staff name is required')
        .isLength({ min: 2, max: 255 })
        .withMessage('Staff name must be between 2 and 255 characters'),
      body('position')
        .notEmpty()
        .withMessage('Position is required')
        .isLength({ min: 2, max: 255 })
        .withMessage('Position must be between 2 and 255 characters'),
      body('phone')
        .optional()
        .isLength({ min: 10, max: 50 })
        .withMessage('Phone must be between 10 and 50 characters'),
      body('email')
        .optional()
        .isEmail()
        .withMessage('Email must be valid'),
      body('hire_date')
        .optional()
        .isISO8601()
        .withMessage('Hire date must be a valid date'),
      body('salary')
        .optional()
        .isFloat({ min: 0 })
        .withMessage('Salary must be a positive number'),
    ];
  }

  public getUpdateValidation() {
    return [
      body('name')
        .optional()
        .isLength({ min: 2, max: 255 })
        .withMessage('Staff name must be between 2 and 255 characters'),
      body('position')
        .optional()
        .isLength({ min: 2, max: 255 })
        .withMessage('Position must be between 2 and 255 characters'),
      body('phone')
        .optional()
        .isLength({ min: 10, max: 50 })
        .withMessage('Phone must be between 10 and 50 characters'),
      body('email')
        .optional()
        .isEmail()
        .withMessage('Email must be valid'),
      body('hire_date')
        .optional()
        .isISO8601()
        .withMessage('Hire date must be a valid date'),
      body('salary')
        .optional()
        .isFloat({ min: 0 })
        .withMessage('Salary must be a positive number'),
      body('is_active')
        .optional()
        .isBoolean()
        .withMessage('Is active must be a boolean'),
    ];
  }
}

const staffController = new StaffController();

export const getStaff = staffController.getAll;
export const getStaffById = staffController.getById;
export const createStaff = staffController.create;
export const updateStaff = staffController.update;
export const deleteStaff = staffController.delete;

export const createStaffValidation = staffController.getCreateValidation();
export const updateStaffValidation = staffController.getUpdateValidation();
export const paginationValidation = staffController.getPaginationValidation();

// Additional staff-specific endpoints
export const getActiveStaff = asyncHandler(async (req: TenantRequest, res: Response) => {
  try {
    const staff = await staffService.getActiveStaff(req.user!.userId, req.tenantId!);

    res.json({
      success: true,
      data: staff,
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      error: error.message,
    });
  }
});

export const getStaffByPosition = asyncHandler(async (req: TenantRequest, res: Response) => {
  try {
    const staff = await staffService.getStaffByPosition(
      req.user!.userId,
      req.tenantId!,
      req.params.position
    );

    res.json({
      success: true,
      data: staff,
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      error: error.message,
    });
  }
});

export const getStaffStats = asyncHandler(async (req: TenantRequest, res: Response) => {
  try {
    const stats = await staffService.getStaffStats(req.user!.userId, req.tenantId!);

    res.json({
      success: true,
      data: stats,
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      error: error.message,
    });
  }
});
