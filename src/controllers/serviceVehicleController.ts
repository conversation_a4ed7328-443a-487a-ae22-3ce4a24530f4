import { Response } from 'express';
import { body } from 'express-validator';
import { BaseCrudController } from './baseCrudController';
import { ServiceVehicle, CreateServiceVehicleRequest, UpdateServiceVehicleRequest } from '../types/models';
import { ServiceVehicleService } from '../services/serviceVehicleService';

const serviceVehicleService = new ServiceVehicleService();

class ServiceVehicleController extends BaseCrudController<ServiceVehicle, CreateServiceVehicleRequest, UpdateServiceVehicleRequest> {
  constructor() {
    super(serviceVehicleService, 'Service vehicle');
  }

  // Override validation for plate number instead of name
  public getCreateValidation() {
    return [
      body('plate_number')
        .notEmpty()
        .withMessage('Plate number is required')
        .isLength({ min: 2, max: 20 })
        .withMessage('Plate number must be between 2 and 20 characters')
        .matches(/^[A-Z0-9\s-]+$/i)
        .withMessage('Plate number can only contain letters, numbers, spaces, and hyphens'),
    ];
  }

  public getUpdateValidation() {
    return [
      body('plate_number')
        .optional()
        .isLength({ min: 2, max: 20 })
        .withMessage('Plate number must be between 2 and 20 characters')
        .matches(/^[A-Z0-9\s-]+$/i)
        .withMessage('Plate number can only contain letters, numbers, spaces, and hyphens'),
    ];
  }
}

const serviceVehicleController = new ServiceVehicleController();

export const getServiceVehicles = serviceVehicleController.getAll;
export const getServiceVehicleById = serviceVehicleController.getById;
export const createServiceVehicle = serviceVehicleController.create;
export const updateServiceVehicle = serviceVehicleController.update;
export const deleteServiceVehicle = serviceVehicleController.delete;

export const createServiceVehicleValidation = serviceVehicleController.getCreateValidation();
export const updateServiceVehicleValidation = serviceVehicleController.getUpdateValidation();
export const paginationValidation = serviceVehicleController.getPaginationValidation();
