import { Response } from 'express';
import { body, query, validationResult } from 'express-validator';
import { UserPaymentService } from '../services/userPaymentService';
import { asyncHandler } from '../middleware/errorHandler';
import { TenantRequest } from '../middleware/tenantIsolation';

const userPaymentService = new UserPaymentService();

export const createPaymentValidation = [
  body('start_date')
    .notEmpty()
    .withMessage('Start date is required')
    .isISO8601()
    .withMessage('Start date must be a valid ISO 8601 date'),
  body('end_date')
    .notEmpty()
    .withMessage('End date is required')
    .isISO8601()
    .withMessage('End date must be a valid ISO 8601 date'),
  body('price')
    .notEmpty()
    .withMessage('Price is required')
    .isFloat({ min: 0.01 })
    .withMessage('Price must be a positive number'),
  body('approval_status')
    .optional()
    .isIn(['onaylandı', 'beklemede', 'reddedildi'])
    .withMessage('Approval status must be one of: onaylandı, beklemede, reddedildi'),
];

export const updatePaymentStatusValidation = [
  body('approval_status')
    .notEmpty()
    .withMessage('Approval status is required')
    .isIn(['onaylandı', 'beklemede', 'reddedildi'])
    .withMessage('Approval status must be one of: onaylandı, beklemede, reddedildi'),
];

export const paginationValidation = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),
];

export const getUserPayments = asyncHandler(async (req: TenantRequest, res: Response) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      error: 'Validation failed',
      details: errors.array(),
    });
  }

  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;

    const result = await userPaymentService.getUserPayments(
      req.user!.userId,
      req.tenantId!,
      page,
      limit
    );

    res.json({
      success: true,
      data: result,
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      error: error.message,
    });
  }
});

export const getPaymentById = asyncHandler(async (req: TenantRequest, res: Response) => {
  try {
    const payment = await userPaymentService.getPaymentById(
      req.params.id,
      req.user!.userId,
      req.tenantId!
    );

    if (!payment) {
      return res.status(404).json({
        success: false,
        error: 'Payment not found',
      });
    }

    res.json({
      success: true,
      data: payment,
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      error: error.message,
    });
  }
});

export const createPayment = asyncHandler(async (req: TenantRequest, res: Response) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      error: 'Validation failed',
      details: errors.array(),
    });
  }

  try {
    const payment = await userPaymentService.createPayment(
      req.body,
      req.user!.userId,
      req.tenantId!
    );

    res.status(201).json({
      success: true,
      data: payment,
      message: 'Payment created successfully',
    });
  } catch (error: any) {
    res.status(400).json({
      success: false,
      error: error.message,
    });
  }
});

export const updatePaymentStatus = asyncHandler(async (req: TenantRequest, res: Response) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      error: 'Validation failed',
      details: errors.array(),
    });
  }

  try {
    const payment = await userPaymentService.updatePaymentStatus(
      req.params.id,
      req.user!.userId,
      req.tenantId!,
      req.body.approval_status
    );

    res.json({
      success: true,
      data: payment,
      message: 'Payment status updated successfully',
    });
  } catch (error: any) {
    res.status(400).json({
      success: false,
      error: error.message,
    });
  }
});

export const getActivePayment = asyncHandler(async (req: TenantRequest, res: Response) => {
  try {
    const payment = await userPaymentService.getActivePayment(req.user!.userId, req.tenantId!);

    if (!payment) {
      return res.status(404).json({
        success: false,
        error: 'No active payment found',
      });
    }

    res.json({
      success: true,
      data: payment,
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      error: error.message,
    });
  }
});

export const getPaymentsByStatus = asyncHandler(async (req: TenantRequest, res: Response) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      error: 'Validation failed',
      details: errors.array(),
    });
  }

  try {
    const status = req.params.status as 'onaylandı' | 'beklemede' | 'reddedildi';
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;

    // Validate status parameter
    if (!['onaylandı', 'beklemede', 'reddedildi'].includes(status)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid status. Must be one of: onaylandı, beklemede, reddedildi',
      });
    }

    const result = await userPaymentService.getPaymentsByStatus(
      req.user!.userId,
      req.tenantId!,
      status,
      page,
      limit
    );

    res.json({
      success: true,
      data: result,
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      error: error.message,
    });
  }
});
