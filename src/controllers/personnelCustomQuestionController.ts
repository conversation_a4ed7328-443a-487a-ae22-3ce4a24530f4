import { Response } from 'express';
import { body } from 'express-validator';
import { BaseCrudController } from './baseCrudController';
import { PersonnelCustomQuestion, CreatePersonnelCustomQuestionRequest, UpdatePersonnelCustomQuestionRequest } from '../types/models';
import { PersonnelCustomQuestionService } from '../services/personnelCustomQuestionService';

const personnelCustomQuestionService = new PersonnelCustomQuestionService();

class PersonnelCustomQuestionController extends BaseCrudController<PersonnelCustomQuestion, CreatePersonnelCustomQuestionRequest, UpdatePersonnelCustomQuestionRequest> {
  constructor() {
    super(personnelCustomQuestionService, 'Personnel custom question');
  }

  // Override validation for question_text instead of name
  public getCreateValidation() {
    return [
      body('question_text')
        .notEmpty()
        .withMessage('Question text is required')
        .isLength({ min: 5 })
        .withMessage('Question text must be at least 5 characters'),
    ];
  }

  public getUpdateValidation() {
    return [
      body('question_text')
        .optional()
        .isLength({ min: 5 })
        .withMessage('Question text must be at least 5 characters'),
    ];
  }
}

const personnelCustomQuestionController = new PersonnelCustomQuestionController();

export const getPersonnelCustomQuestions = personnelCustomQuestionController.getAll;
export const getPersonnelCustomQuestionById = personnelCustomQuestionController.getById;
export const createPersonnelCustomQuestion = personnelCustomQuestionController.create;
export const updatePersonnelCustomQuestion = personnelCustomQuestionController.update;
export const deletePersonnelCustomQuestion = personnelCustomQuestionController.delete;

export const createPersonnelCustomQuestionValidation = personnelCustomQuestionController.getCreateValidation();
export const updatePersonnelCustomQuestionValidation = personnelCustomQuestionController.getUpdateValidation();
export const paginationValidation = personnelCustomQuestionController.getPaginationValidation();
