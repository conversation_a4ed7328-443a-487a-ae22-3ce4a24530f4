import { Response } from 'express';
import { body, query, validationResult } from 'express-validator';
import { as<PERSON><PERSON><PERSON><PERSON> } from '../middleware/errorHandler';
import { TenantRequest } from '../middleware/tenantIsolation';
import { BaseCrudService } from '../services/baseCrudService';

export abstract class BaseCrudController<T, CreateRequest extends Record<string, any>, UpdateRequest extends Record<string, any>> {
  protected service: BaseCrudService<T, CreateRequest, UpdateRequest>;
  protected entityName: string;

  constructor(service: BaseCrudService<T, CreateRequest, UpdateRequest>, entityName: string) {
    this.service = service;
    this.entityName = entityName;
  }

  // Common validation rules
  public getCreateValidation() {
    return [
      body('name')
        .notEmpty()
        .withMessage(`${this.entityName} name is required`)
        .isLength({ min: 2, max: 255 })
        .withMessage(`${this.entityName} name must be between 2 and 255 characters`),
    ];
  }

  public getUpdateValidation() {
    return [
      body('name')
        .optional()
        .isLength({ min: 2, max: 255 })
        .withMessage(`${this.entityName} name must be between 2 and 255 characters`),
    ];
  }

  public getPaginationValidation() {
    return [
      query('page')
        .optional()
        .isInt({ min: 1 })
        .withMessage('Page must be a positive integer'),
      query('limit')
        .optional()
        .isInt({ min: 1, max: 100 })
        .withMessage('Limit must be between 1 and 100'),
      query('search')
        .optional()
        .isLength({ min: 1, max: 255 })
        .withMessage('Search term must be between 1 and 255 characters'),
    ];
  }

  getAll = asyncHandler(async (req: TenantRequest, res: Response) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array(),
      });
    }

    try {
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 50;
      const search = req.query.search as string;

      let result;
      if (search) {
        result = await this.service.search(req.user!.userId, req.tenantId!, search, page, limit);
      } else {
        result = await this.service.getAll(req.user!.userId, req.tenantId!, page, limit);
      }

      res.json({
        success: true,
        data: result,
      });
    } catch (error: any) {
      res.status(500).json({
        success: false,
        error: error.message,
      });
    }
  });

  getById = asyncHandler(async (req: TenantRequest, res: Response) => {
    try {
      const item = await this.service.getById(req.params.id, req.user!.userId, req.tenantId!);

      if (!item) {
        return res.status(404).json({
          success: false,
          error: `${this.entityName} not found`,
        });
      }

      res.json({
        success: true,
        data: item,
      });
    } catch (error: any) {
      res.status(500).json({
        success: false,
        error: error.message,
      });
    }
  });

  create = asyncHandler(async (req: TenantRequest, res: Response) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array(),
      });
    }

    try {
      const item = await this.service.create(req.body, req.user!.userId, req.tenantId!);

      res.status(201).json({
        success: true,
        data: item,
        message: `${this.entityName} created successfully`,
      });
    } catch (error: any) {
      res.status(400).json({
        success: false,
        error: error.message,
      });
    }
  });

  update = asyncHandler(async (req: TenantRequest, res: Response) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array(),
      });
    }

    try {
      const item = await this.service.update(req.params.id, req.body, req.user!.userId, req.tenantId!);

      res.json({
        success: true,
        data: item,
        message: `${this.entityName} updated successfully`,
      });
    } catch (error: any) {
      res.status(400).json({
        success: false,
        error: error.message,
      });
    }
  });

  delete = asyncHandler(async (req: TenantRequest, res: Response) => {
    try {
      await this.service.delete(req.params.id, req.user!.userId, req.tenantId!);

      res.json({
        success: true,
        message: `${this.entityName} deleted successfully`,
      });
    } catch (error: any) {
      res.status(400).json({
        success: false,
        error: error.message,
      });
    }
  });
}
