import { Response } from 'express';
import { body } from 'express-validator';
import { BaseCrudController } from './baseCrudController';
import { CustomerCustomQuestion, CreateCustomerCustomQuestionRequest, UpdateCustomerCustomQuestionRequest } from '../types/models';
import { CustomerCustomQuestionService } from '../services/customerCustomQuestionService';

const customerCustomQuestionService = new CustomerCustomQuestionService();

class CustomerCustomQuestionController extends BaseCrudController<CustomerCustomQuestion, CreateCustomerCustomQuestionRequest, UpdateCustomerCustomQuestionRequest> {
  constructor() {
    super(customerCustomQuestionService, 'Customer custom question');
  }

  // Override validation for question_text instead of name
  public getCreateValidation() {
    return [
      body('question_text')
        .notEmpty()
        .withMessage('Question text is required')
        .isLength({ min: 5 })
        .withMessage('Question text must be at least 5 characters'),
    ];
  }

  public getUpdateValidation() {
    return [
      body('question_text')
        .optional()
        .isLength({ min: 5 })
        .withMessage('Question text must be at least 5 characters'),
    ];
  }
}

const customerCustomQuestionController = new CustomerCustomQuestionController();

export const getCustomerCustomQuestions = customerCustomQuestionController.getAll;
export const getCustomerCustomQuestionById = customerCustomQuestionController.getById;
export const createCustomerCustomQuestion = customerCustomQuestionController.create;
export const updateCustomerCustomQuestion = customerCustomQuestionController.update;
export const deleteCustomerCustomQuestion = customerCustomQuestionController.delete;

export const createCustomerCustomQuestionValidation = customerCustomQuestionController.getCreateValidation();
export const updateCustomerCustomQuestionValidation = customerCustomQuestionController.getUpdateValidation();
export const paginationValidation = customerCustomQuestionController.getPaginationValidation();
