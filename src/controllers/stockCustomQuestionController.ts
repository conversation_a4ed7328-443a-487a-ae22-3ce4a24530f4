import { Response } from 'express';
import { body } from 'express-validator';
import { BaseCrudController } from './baseCrudController';
import { StockCustomQuestion, CreateStockCustomQuestionRequest, UpdateStockCustomQuestionRequest } from '../types/models';
import { StockCustomQuestionService } from '../services/stockCustomQuestionService';

const stockCustomQuestionService = new StockCustomQuestionService();

class StockCustomQuestionController extends BaseCrudController<StockCustomQuestion, CreateStockCustomQuestionRequest, UpdateStockCustomQuestionRequest> {
  constructor() {
    super(stockCustomQuestionService, 'Stock custom question');
  }

  // Override validation for question_text instead of name
  public getCreateValidation() {
    return [
      body('question_text')
        .notEmpty()
        .withMessage('Question text is required')
        .isLength({ min: 5 })
        .withMessage('Question text must be at least 5 characters'),
    ];
  }

  public getUpdateValidation() {
    return [
      body('question_text')
        .optional()
        .isLength({ min: 5 })
        .withMessage('Question text must be at least 5 characters'),
    ];
  }
}

const stockCustomQuestionController = new StockCustomQuestionController();

export const getStockCustomQuestions = stockCustomQuestionController.getAll;
export const getStockCustomQuestionById = stockCustomQuestionController.getById;
export const createStockCustomQuestion = stockCustomQuestionController.create;
export const updateStockCustomQuestion = stockCustomQuestionController.update;
export const deleteStockCustomQuestion = stockCustomQuestionController.delete;

export const createStockCustomQuestionValidation = stockCustomQuestionController.getCreateValidation();
export const updateStockCustomQuestionValidation = stockCustomQuestionController.getUpdateValidation();
export const paginationValidation = stockCustomQuestionController.getPaginationValidation();
