import { Response } from 'express';
import { body, query, validationResult } from 'express-validator';
import { CashTransactionService } from '../services/cashTransactionService';
import { asyncHandler } from '../middleware/errorHandler';
import { TenantRequest } from '../middleware/tenantIsolation';

const cashTransactionService = new CashTransactionService();

export const createCashTransactionValidation = [
  body('amount')
    .notEmpty()
    .withMessage('Amount is required')
    .isFloat({ min: 0.01 })
    .withMessage('Amount must be a positive number'),
  body('transaction_type')
    .notEmpty()
    .withMessage('Transaction type is required')
    .isIn(['income', 'expense'])
    .withMessage('Transaction type must be either income or expense'),
  body('description')
    .notEmpty()
    .withMessage('Description is required')
    .isLength({ min: 3, max: 1000 })
    .withMessage('Description must be between 3 and 1000 characters'),
  body('category')
    .optional()
    .isLength({ min: 1, max: 100 })
    .withMessage('Category must be between 1 and 100 characters'),
  body('reference_id')
    .optional()
    .isUUID()
    .withMessage('Reference ID must be a valid UUID'),
  body('reference_type')
    .optional()
    .isLength({ min: 1, max: 50 })
    .withMessage('Reference type must be between 1 and 50 characters'),
  body('transaction_date')
    .optional()
    .isISO8601()
    .withMessage('Transaction date must be a valid date'),
];

export const updateCashTransactionValidation = [
  body('amount')
    .optional()
    .isFloat({ min: 0.01 })
    .withMessage('Amount must be a positive number'),
  body('transaction_type')
    .optional()
    .isIn(['income', 'expense'])
    .withMessage('Transaction type must be either income or expense'),
  body('description')
    .optional()
    .isLength({ min: 3, max: 1000 })
    .withMessage('Description must be between 3 and 1000 characters'),
  body('category')
    .optional()
    .isLength({ min: 1, max: 100 })
    .withMessage('Category must be between 1 and 100 characters'),
  body('reference_id')
    .optional()
    .isUUID()
    .withMessage('Reference ID must be a valid UUID'),
  body('reference_type')
    .optional()
    .isLength({ min: 1, max: 50 })
    .withMessage('Reference type must be between 1 and 50 characters'),
  body('transaction_date')
    .optional()
    .isISO8601()
    .withMessage('Transaction date must be a valid date'),
];

export const paginationValidation = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),
  query('search')
    .optional()
    .isLength({ min: 1, max: 255 })
    .withMessage('Search term must be between 1 and 255 characters'),
  query('transaction_type')
    .optional()
    .isIn(['income', 'expense'])
    .withMessage('Transaction type must be either income or expense'),
  query('category')
    .optional()
    .isLength({ min: 1, max: 100 })
    .withMessage('Category must be between 1 and 100 characters'),
  query('start_date')
    .optional()
    .isISO8601()
    .withMessage('Start date must be a valid date'),
  query('end_date')
    .optional()
    .isISO8601()
    .withMessage('End date must be a valid date'),
];

export const getCashTransactions = asyncHandler(async (req: TenantRequest, res: Response) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      error: 'Validation failed',
      details: errors.array(),
    });
  }

  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 50;
    const search = req.query.search as string;
    const transactionType = req.query.transaction_type as string;
    const category = req.query.category as string;
    const startDate = req.query.start_date as string;
    const endDate = req.query.end_date as string;

    let result;
    if (search) {
      result = await cashTransactionService.search(req.user!.userId, req.tenantId!, search, page, limit);
    } else {
      result = await cashTransactionService.getAll(
        req.user!.userId,
        req.tenantId!,
        page,
        limit,
        transactionType,
        category,
        startDate,
        endDate
      );
    }

    res.json({
      success: true,
      data: result,
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      error: error.message,
    });
  }
});

export const getCashTransactionById = asyncHandler(async (req: TenantRequest, res: Response) => {
  try {
    const transaction = await cashTransactionService.getById(req.params.id, req.user!.userId, req.tenantId!);

    if (!transaction) {
      return res.status(404).json({
        success: false,
        error: 'Cash transaction not found',
      });
    }

    res.json({
      success: true,
      data: transaction,
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      error: error.message,
    });
  }
});

export const createCashTransaction = asyncHandler(async (req: TenantRequest, res: Response) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      error: 'Validation failed',
      details: errors.array(),
    });
  }

  try {
    const transaction = await cashTransactionService.create(req.body, req.user!.userId, req.tenantId!);

    res.status(201).json({
      success: true,
      data: transaction,
      message: 'Cash transaction created successfully',
    });
  } catch (error: any) {
    res.status(400).json({
      success: false,
      error: error.message,
    });
  }
});

export const updateCashTransaction = asyncHandler(async (req: TenantRequest, res: Response) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      error: 'Validation failed',
      details: errors.array(),
    });
  }

  try {
    const transaction = await cashTransactionService.update(
      req.params.id,
      req.body,
      req.user!.userId,
      req.tenantId!
    );

    res.json({
      success: true,
      data: transaction,
      message: 'Cash transaction updated successfully',
    });
  } catch (error: any) {
    res.status(400).json({
      success: false,
      error: error.message,
    });
  }
});

export const deleteCashTransaction = asyncHandler(async (req: TenantRequest, res: Response) => {
  try {
    await cashTransactionService.delete(req.params.id, req.user!.userId, req.tenantId!);

    res.json({
      success: true,
      message: 'Cash transaction deleted successfully',
    });
  } catch (error: any) {
    res.status(400).json({
      success: false,
      error: error.message,
    });
  }
});

export const getCashSummary = asyncHandler(async (req: TenantRequest, res: Response) => {
  try {
    const startDate = req.query.start_date as string;
    const endDate = req.query.end_date as string;
    
    const summary = await cashTransactionService.getCashSummary(
      req.user!.userId,
      req.tenantId!,
      startDate,
      endDate
    );

    res.json({
      success: true,
      data: summary,
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      error: error.message,
    });
  }
});

export const getTransactionsByCategory = asyncHandler(async (req: TenantRequest, res: Response) => {
  try {
    const categories = await cashTransactionService.getTransactionsByCategory(
      req.user!.userId,
      req.tenantId!
    );

    res.json({
      success: true,
      data: categories,
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      error: error.message,
    });
  }
});
