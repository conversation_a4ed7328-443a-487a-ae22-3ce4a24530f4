import bcrypt from 'bcryptjs';
import { query } from './connection';
import { createTables } from './migrate';

const seedDatabase = async () => {
  try {
    console.log('🌱 Starting database seeding...');

    // First run migrations to ensure tables exist
    await createTables();

    // Create default tenant
    const tenantResult = await query(`
      INSERT INTO tenants (name, slug, is_active)
      VALUES ('Demo Company', 'demo-company', true)
      ON CONFLICT (slug) DO NOTHING
      RETURNING id
    `);

    let tenantId: string;
    if (tenantResult.rows.length > 0) {
      tenantId = tenantResult.rows[0].id;
      console.log('✅ Created default tenant');
    } else {
      // Get existing tenant
      const existingTenant = await query('SELECT id FROM tenants WHERE slug = $1', ['demo-company']);
      tenantId = existingTenant.rows[0].id;
      console.log('✅ Using existing default tenant');
    }

    // Create admin user
    const adminEmail = process.env.ADMIN_EMAIL || '<EMAIL>';
    const adminPassword = process.env.ADMIN_PASSWORD || 'admin123';
    const hashedPassword = await bcrypt.hash(adminPassword, 12);

    await query(`
      INSERT INTO users (name, email, password, role, tenant_id, is_active)
      VALUES ('System Admin', $1, $2, 'admin', $3, true)
      ON CONFLICT (email) DO NOTHING
    `, [adminEmail, hashedPassword, tenantId]);
    console.log('✅ Created admin user');

    // Create sample operator user
    const operatorPassword = await bcrypt.hash('operator123', 12);
    await query(`
      INSERT INTO users (name, email, password, role, tenant_id, is_active)
      VALUES ('John Operator', '<EMAIL>', $1, 'operator', $2, true)
      ON CONFLICT (email) DO NOTHING
    `, [operatorPassword, tenantId]);
    console.log('✅ Created operator user');

    // Create sample technician user
    const technicianPassword = await bcrypt.hash('tech123', 12);
    await query(`
      INSERT INTO users (name, email, password, role, tenant_id, is_active)
      VALUES ('Mike Technician', '<EMAIL>', $1, 'technician', $2, true)
      ON CONFLICT (email) DO NOTHING
    `, [technicianPassword, tenantId]);
    console.log('✅ Created technician user');

    // Create sample viewer user
    const viewerPassword = await bcrypt.hash('viewer123', 12);
    await query(`
      INSERT INTO users (name, email, password, role, tenant_id, is_active)
      VALUES ('Sarah Viewer', '<EMAIL>', $1, 'viewer', $2, true)
      ON CONFLICT (email) DO NOTHING
    `, [viewerPassword, tenantId]);
    console.log('✅ Created viewer user');

    // Get admin user ID for creating sample data
    const adminUser = await query('SELECT id FROM users WHERE email = $1', [adminEmail]);
    const adminUserId = adminUser.rows[0].id;

    // Create sample customers
    const customer1Result = await query(`
      INSERT INTO customers (name, phone, email, address, tenant_id, created_by)
      VALUES ('John Smith', '+1234567890', '<EMAIL>', '123 Main St, City', $1, $2)
      ON CONFLICT DO NOTHING
      RETURNING id
    `, [tenantId, adminUserId]);

    if (customer1Result.rows.length > 0) {
      const customerId = customer1Result.rows[0].id;

      // Create sample device for customer
      const deviceResult = await query(`
        INSERT INTO devices (customer_id, brand, model, serial_number, warranty_expiry, tenant_id)
        VALUES ($1, 'Apple', 'iPhone 13', 'ABC123456789', '2024-12-31', $2)
        RETURNING id
      `, [customerId, tenantId]);

      if (deviceResult.rows.length > 0) {
        const deviceId = deviceResult.rows[0].id;

        // Create sample service request
        await query(`
          INSERT INTO service_requests (device_id, status, description, request_date, tenant_id)
          VALUES ($1, 'Pending', 'Screen replacement needed', CURRENT_DATE, $2)
        `, [deviceId, tenantId]);
        console.log('✅ Created sample service request');
      }

      console.log('✅ Created sample customer and device');
    }

    // Create sample stock items
    await query(`
      INSERT INTO stock_items (name, code, quantity, minimum_threshold, price, tenant_id)
      VALUES 
        ('iPhone Screen', 'IPHONE-SCREEN-001', 10, 5, 150.00, $1),
        ('Samsung Battery', 'SAMSUNG-BAT-001', 25, 10, 45.00, $1),
        ('Charging Cable', 'CABLE-USB-C-001', 50, 20, 15.00, $1)
      ON CONFLICT (code) DO NOTHING
    `, [tenantId]);
    console.log('✅ Created sample stock items');

    console.log('🎉 Database seeding completed successfully!');
    console.log('');
    console.log('📋 Default Login Credentials:');
    console.log(`Admin: ${adminEmail} / ${adminPassword}`);
    console.log('Operator: <EMAIL> / operator123');
    console.log('Technician: <EMAIL> / tech123');
    console.log('Viewer: <EMAIL> / viewer123');

  } catch (error) {
    console.error('❌ Database seeding failed:', error);
    throw error;
  }
};

// Run seeding if called directly
if (require.main === module) {
  seedDatabase()
    .then(() => process.exit(0))
    .catch(() => process.exit(1));
}

export { seedDatabase };
