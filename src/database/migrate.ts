import { query } from './connection';

const createTables = async () => {
  try {
    console.log('🔄 Starting database migration...');

    // Enable UUID extension
    await query(`CREATE EXTENSION IF NOT EXISTS "uuid-ossp";`);

    // Create Tenants table
    await query(`
      CREATE TABLE IF NOT EXISTS tenants (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        name VARCHAR(255) NOT NULL,
        slug VARCHAR(100) NOT NULL UNIQUE,
        logo VARCHAR(500),
        is_active BOOLEAN DEFAULT true,
        deleted_at TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT tenants_slug_check CHECK (slug ~ '^[a-z0-9-]+$'),
        CONSTRAINT tenants_name_length CHECK (length(name) >= 2)
      );
    `);

    // Create Users table
    await query(`
      CREATE TABLE IF NOT EXISTS users (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        name VARCHAR(255) NOT NULL,
        email VARCHAR(255) NOT NULL UNIQUE,
        password VARCHAR(255) NOT NULL,
        role VARCHAR(50) NOT NULL CHECK (role IN ('admin', 'technician', 'operator', 'viewer')),
        tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
        is_active BOOLEAN DEFAULT true,
        deleted_at TIMESTAMP NULL,
        last_login TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT users_email_format CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$'),
        CONSTRAINT users_name_length CHECK (length(name) >= 2)
      );
    `);

    // Create Customers table
    await query(`
      CREATE TABLE IF NOT EXISTS customers (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        name VARCHAR(255) NOT NULL,
        phone VARCHAR(50) NOT NULL,
        email VARCHAR(255),
        address TEXT,
        tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
        created_by UUID NOT NULL REFERENCES users(id),
        deleted_at TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT customers_name_length CHECK (length(name) >= 2),
        -- CONSTRAINT customers_phone_format CHECK (phone ~ '^[+]?[0-9\\s\\-\\(\\)]+$'),
        CONSTRAINT customers_email_format CHECK (email IS NULL OR email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$')
      );
    `);

    // Create Devices table
    await query(`
      CREATE TABLE IF NOT EXISTS devices (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        customer_id UUID NOT NULL REFERENCES customers(id) ON DELETE CASCADE,
        brand VARCHAR(255) NOT NULL,
        model VARCHAR(255) NOT NULL,
        serial_number VARCHAR(255),
        warranty_expiry DATE,
        tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
        deleted_at TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT devices_brand_length CHECK (length(brand) >= 1),
        CONSTRAINT devices_model_length CHECK (length(model) >= 1),
        CONSTRAINT devices_warranty_future CHECK (warranty_expiry IS NULL OR warranty_expiry >= CURRENT_DATE),
        UNIQUE(serial_number, tenant_id)
      );
    `);

    // Create Service Requests table
    await query(`
      CREATE TABLE IF NOT EXISTS service_requests (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        device_id UUID NOT NULL REFERENCES devices(id) ON DELETE CASCADE,
        status VARCHAR(50) NOT NULL DEFAULT 'Pending' CHECK (status IN ('Pending', 'Assigned', 'OnTheWay', 'InRepair', 'Completed')),
        description TEXT NOT NULL,
        assigned_to UUID REFERENCES users(id),
        request_date DATE DEFAULT CURRENT_DATE,
        estimated_completion DATE,
        completed_at TIMESTAMP NULL,
        priority VARCHAR(20) DEFAULT 'Medium' CHECK (priority IN ('Low', 'Medium', 'High', 'Urgent')),
        tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
        deleted_at TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT service_requests_description_length CHECK (length(description) >= 10),
        CONSTRAINT service_requests_completion_date CHECK (estimated_completion IS NULL OR estimated_completion >= request_date)
      );
    `);

    // Create Service Notes table
    await query(`
      CREATE TABLE IF NOT EXISTS service_notes (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        service_request_id UUID NOT NULL REFERENCES service_requests(id) ON DELETE CASCADE,
        note TEXT NOT NULL,
        photo VARCHAR(500),
        created_by UUID NOT NULL REFERENCES users(id),
        tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
        deleted_at TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT service_notes_note_length CHECK (length(note) >= 5)
      );
    `);

    // Create Invoices table
    await query(`
      CREATE TABLE IF NOT EXISTS invoices (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        service_request_id UUID NOT NULL REFERENCES service_requests(id) ON DELETE CASCADE,
        invoice_number VARCHAR(50) UNIQUE NOT NULL,
        amount DECIMAL(10,2) NOT NULL,
        tax_amount DECIMAL(10,2) DEFAULT 0,
        total_amount DECIMAL(10,2) GENERATED ALWAYS AS (amount + tax_amount) STORED,
        paid BOOLEAN DEFAULT false,
        issue_date DATE DEFAULT CURRENT_DATE,
        due_date DATE,
        paid_date DATE NULL,
        invoice_pdf VARCHAR(500),
        tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
        deleted_at TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT invoices_amount_positive CHECK (amount > 0),
        CONSTRAINT invoices_tax_non_negative CHECK (tax_amount >= 0),
        CONSTRAINT invoices_due_date_check CHECK (due_date IS NULL OR due_date >= issue_date),
        CONSTRAINT invoices_paid_date_check CHECK (paid_date IS NULL OR paid_date >= issue_date)
      );
    `);

    // Create Stock Items table
    await query(`
      CREATE TABLE IF NOT EXISTS stock_items (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        name VARCHAR(255) NOT NULL,
        code VARCHAR(100),
        description TEXT,
        quantity INTEGER DEFAULT 0,
        minimum_threshold INTEGER DEFAULT 0,
        price DECIMAL(10,2),
        cost DECIMAL(10,2),
        category VARCHAR(100),
        tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
        deleted_at TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT stock_items_name_length CHECK (length(name) >= 2),
        CONSTRAINT stock_items_quantity_non_negative CHECK (quantity >= 0),
        CONSTRAINT stock_items_threshold_non_negative CHECK (minimum_threshold >= 0),
        CONSTRAINT stock_items_price_non_negative CHECK (price IS NULL OR price >= 0),
        CONSTRAINT stock_items_cost_non_negative CHECK (cost IS NULL OR cost >= 0),
        UNIQUE(code, tenant_id)
      );
    `);

    // Create Payments table
    await query(`
      CREATE TABLE IF NOT EXISTS payments (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        invoice_id UUID NOT NULL REFERENCES invoices(id) ON DELETE CASCADE,
        amount DECIMAL(10,2) NOT NULL,
        payment_method VARCHAR(50) NOT NULL CHECK (payment_method IN ('Cash', 'Card', 'Online', 'Bank Transfer', 'Check')),
        transaction_id VARCHAR(255),
        paid_by VARCHAR(255),
        notes TEXT,
        paid_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
        deleted_at TIMESTAMP NULL,
        CONSTRAINT payments_amount_positive CHECK (amount > 0)
      );
    `);

    // Create Reminders table
    await query(`
      CREATE TABLE IF NOT EXISTS reminders (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        customer_id UUID NOT NULL REFERENCES customers(id) ON DELETE CASCADE,
        title VARCHAR(255) NOT NULL,
        description TEXT,
        remind_date DATE NOT NULL,
        remind_time TIME,
        is_sent BOOLEAN DEFAULT false,
        sent_at TIMESTAMP NULL,
        reminder_type VARCHAR(50) DEFAULT 'General' CHECK (reminder_type IN ('General', 'Warranty', 'Maintenance', 'Follow-up')),
        tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
        deleted_at TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT reminders_title_length CHECK (length(title) >= 3),
        CONSTRAINT reminders_future_date CHECK (remind_date >= CURRENT_DATE)
      );
    `);

    // Create Messages table
    await query(`
      CREATE TABLE IF NOT EXISTS messages (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        from_user_id UUID NOT NULL REFERENCES users(id),
        to_user_id UUID NOT NULL REFERENCES users(id),
        subject VARCHAR(255),
        content TEXT NOT NULL,
        message_type VARCHAR(50) DEFAULT 'Direct' CHECK (message_type IN ('Direct', 'Broadcast', 'System')),
        read BOOLEAN DEFAULT false,
        read_at TIMESTAMP NULL,
        priority VARCHAR(20) DEFAULT 'Normal' CHECK (priority IN ('Low', 'Normal', 'High')),
        tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
        deleted_at TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT messages_content_length CHECK (length(content) >= 1),
        CONSTRAINT messages_different_users CHECK (from_user_id != to_user_id)
      );
    `);

    // Create Company Infos table
    await query(`
      CREATE TABLE IF NOT EXISTS company_infos (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        company_name VARCHAR(255) NOT NULL,
        phone1 VARCHAR(50) NOT NULL,
        phone2 VARCHAR(50),
        city VARCHAR(100) NOT NULL,
        district VARCHAR(100) NOT NULL,
        address TEXT NOT NULL,
        email VARCHAR(255),
        website VARCHAR(255),
        tax_number VARCHAR(50),
        tax_office VARCHAR(100),
        tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
        deleted_at TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT company_infos_company_name_length CHECK (length(company_name) >= 2),
        CONSTRAINT company_infos_email_format CHECK (email IS NULL OR email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$'),
        CONSTRAINT company_infos_website_format CHECK (website IS NULL OR website ~* '^https?://'),
        UNIQUE(user_id, tenant_id)
      );
    `);

    // Create User Payments table
    await query(`
      CREATE TABLE IF NOT EXISTS user_payments (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        start_date DATE NOT NULL,
        end_date DATE NOT NULL,
        price DECIMAL(10,2) NOT NULL,
        approval_status VARCHAR(20) DEFAULT 'beklemede' CHECK (approval_status IN ('onaylandı', 'beklemede', 'reddedildi')),
        tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
        deleted_at TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT user_payments_price_positive CHECK (price > 0),
        CONSTRAINT user_payments_date_order CHECK (end_date > start_date)
      );
    `);

    // Create SMS Settings table
    await query(`
      CREATE TABLE IF NOT EXISTS sms_settings (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        sender_company VARCHAR(255) NOT NULL,
        message_header VARCHAR(100) NOT NULL,
        username VARCHAR(100) NOT NULL,
        password VARCHAR(255) NOT NULL,
        sms_template TEXT NOT NULL,
        tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
        deleted_at TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT sms_settings_sender_company_length CHECK (length(sender_company) >= 2),
        CONSTRAINT sms_settings_message_header_length CHECK (length(message_header) >= 1),
        CONSTRAINT sms_settings_template_length CHECK (length(sms_template) >= 10),
        UNIQUE(user_id, tenant_id)
      );
    `);

    // Create Service Steps table
    await query(`
      CREATE TABLE IF NOT EXISTS service_steps (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        name VARCHAR(255) NOT NULL,
        step_order INTEGER NOT NULL,
        tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
        deleted_at TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT service_steps_name_length CHECK (length(name) >= 2),
        CONSTRAINT service_steps_order_positive CHECK (step_order > 0),
        UNIQUE(user_id, step_order, tenant_id)
      );
    `);

    // Create Service Step Questions table
    await query(`
      CREATE TABLE IF NOT EXISTS service_step_questions (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        step_id UUID NOT NULL REFERENCES service_steps(id) ON DELETE CASCADE,
        question_text TEXT NOT NULL,
        answer_type VARCHAR(50) NOT NULL CHECK (answer_type IN ('Açıklama', 'Tarih', 'Seçim', 'Sayı', 'Evet/Hayır')),
        question_order INTEGER NOT NULL,
        tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
        deleted_at TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT service_step_questions_text_length CHECK (length(question_text) >= 5),
        CONSTRAINT service_step_questions_order_positive CHECK (question_order > 0),
        UNIQUE(step_id, question_order, tenant_id)
      );
    `);

    // Create Device Brands table
    await query(`
      CREATE TABLE IF NOT EXISTS device_brands (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        name VARCHAR(255) NOT NULL,
        tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
        deleted_at TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT device_brands_name_length CHECK (length(name) >= 2),
        UNIQUE(user_id, name, tenant_id)
      );
    `);

    // Create Device Types table
    await query(`
      CREATE TABLE IF NOT EXISTS device_types (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        name VARCHAR(255) NOT NULL,
        tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
        deleted_at TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT device_types_name_length CHECK (length(name) >= 2),
        UNIQUE(user_id, name, tenant_id)
      );
    `);

    // Create Service Sources table
    await query(`
      CREATE TABLE IF NOT EXISTS service_sources (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        name VARCHAR(255) NOT NULL,
        tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
        deleted_at TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT service_sources_name_length CHECK (length(name) >= 2),
        UNIQUE(user_id, name, tenant_id)
      );
    `);

    // Create Service Vehicles table
    await query(`
      CREATE TABLE IF NOT EXISTS service_vehicles (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        plate_number VARCHAR(20) NOT NULL,
        tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
        deleted_at TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT service_vehicles_plate_length CHECK (length(plate_number) >= 2),
        UNIQUE(user_id, plate_number, tenant_id)
      );
    `);

    // Create Customer Categories table
    await query(`
      CREATE TABLE IF NOT EXISTS customer_categories (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        name VARCHAR(255) NOT NULL,
        parent_id UUID REFERENCES customer_categories(id) ON DELETE CASCADE,
        tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
        deleted_at TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT customer_categories_name_length CHECK (length(name) >= 2),
        UNIQUE(user_id, name, tenant_id)
      );
    `);

    // Create Customer Custom Questions table
    await query(`
      CREATE TABLE IF NOT EXISTS customer_custom_questions (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        question_text TEXT NOT NULL,
        tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
        deleted_at TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT customer_custom_questions_text_length CHECK (length(question_text) >= 5)
      );
    `);

    // Create Personnel Positions table
    await query(`
      CREATE TABLE IF NOT EXISTS personnel_positions (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        name VARCHAR(255) NOT NULL,
        tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
        deleted_at TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT personnel_positions_name_length CHECK (length(name) >= 2),
        UNIQUE(user_id, name, tenant_id)
      );
    `);

    // Create Personnel Custom Questions table
    await query(`
      CREATE TABLE IF NOT EXISTS personnel_custom_questions (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        question_text TEXT NOT NULL,
        tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
        deleted_at TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT personnel_custom_questions_text_length CHECK (length(question_text) >= 5)
      );
    `);

    // Create Stock Categories table
    await query(`
      CREATE TABLE IF NOT EXISTS stock_categories (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        name VARCHAR(255) NOT NULL,
        parent_id UUID REFERENCES stock_categories(id) ON DELETE CASCADE,
        tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
        deleted_at TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT stock_categories_name_length CHECK (length(name) >= 2),
        UNIQUE(user_id, name, tenant_id)
      );
    `);

    // Create Warehouses table
    await query(`
      CREATE TABLE IF NOT EXISTS warehouses (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        name VARCHAR(255) NOT NULL,
        parent_id UUID REFERENCES warehouses(id) ON DELETE CASCADE,
        tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
        deleted_at TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT warehouses_name_length CHECK (length(name) >= 2),
        UNIQUE(user_id, name, tenant_id)
      );
    `);

    // Create Suppliers table
    await query(`
      CREATE TABLE IF NOT EXISTS suppliers (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        name VARCHAR(255) NOT NULL,
        tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
        deleted_at TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT suppliers_name_length CHECK (length(name) >= 2),
        UNIQUE(user_id, name, tenant_id)
      );
    `);

    // Create Stock Custom Questions table
    await query(`
      CREATE TABLE IF NOT EXISTS stock_custom_questions (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        question_text TEXT NOT NULL,
        tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
        deleted_at TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT stock_custom_questions_text_length CHECK (length(question_text) >= 5)
      );
    `);

    // Create Services table
    await query(`
      CREATE TABLE IF NOT EXISTS services (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        customer_id UUID NOT NULL REFERENCES customers(id) ON DELETE CASCADE,
        service_type VARCHAR(50) NOT NULL DEFAULT 'normal' CHECK (service_type IN ('normal', 'periodic', 'warranty', 'emergency')),
        title VARCHAR(255) NOT NULL,
        description TEXT,
        status VARCHAR(50) NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'in_progress', 'completed', 'cancelled')),
        priority VARCHAR(20) DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
        assigned_to UUID REFERENCES users(id),
        estimated_completion_date DATE,
        completed_at TIMESTAMP NULL,
        total_cost DECIMAL(10,2) DEFAULT 0,
        tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
        deleted_at TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT services_title_length CHECK (length(title) >= 3),
        CONSTRAINT services_cost_non_negative CHECK (total_cost >= 0)
      );
    `);

    // Create Staff table
    await query(`
      CREATE TABLE IF NOT EXISTS staff (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        name VARCHAR(255) NOT NULL,
        position VARCHAR(255) NOT NULL,
        phone VARCHAR(50),
        email VARCHAR(255),
        hire_date DATE DEFAULT CURRENT_DATE,
        salary DECIMAL(10,2),
        is_active BOOLEAN DEFAULT true,
        tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
        deleted_at TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT staff_name_length CHECK (length(name) >= 2),
        CONSTRAINT staff_position_length CHECK (length(position) >= 2),
        CONSTRAINT staff_salary_non_negative CHECK (salary IS NULL OR salary >= 0),
        CONSTRAINT staff_email_format CHECK (email IS NULL OR email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+.[A-Za-z]{2,}$')
      );
    `);

    // Create Cash Transactions table
    await query(`
      CREATE TABLE IF NOT EXISTS cash_transactions (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        amount DECIMAL(10,2) NOT NULL,
        transaction_type VARCHAR(20) NOT NULL CHECK (transaction_type IN ('income', 'expense')),
        category VARCHAR(100),
        description TEXT NOT NULL,
        reference_id UUID NULL,
        reference_type VARCHAR(50) NULL,
        transaction_date DATE DEFAULT CURRENT_DATE,
        tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
        deleted_at TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT cash_transactions_amount_positive CHECK (amount > 0),
        CONSTRAINT cash_transactions_description_length CHECK (length(description) >= 3)
      );
    `);

    // Create indexes for better performance
    console.log('Creating database indexes...');

    // Tenant isolation indexes (most important)
    await query(`CREATE INDEX IF NOT EXISTS idx_users_tenant_id ON users(tenant_id) WHERE deleted_at IS NULL;`);
    await query(`CREATE INDEX IF NOT EXISTS idx_customers_tenant_id ON customers(tenant_id) WHERE deleted_at IS NULL;`);
    await query(`CREATE INDEX IF NOT EXISTS idx_devices_tenant_id ON devices(tenant_id) WHERE deleted_at IS NULL;`);
    await query(`CREATE INDEX IF NOT EXISTS idx_service_requests_tenant_id ON service_requests(tenant_id) WHERE deleted_at IS NULL;`);
    await query(`CREATE INDEX IF NOT EXISTS idx_service_notes_tenant_id ON service_notes(tenant_id) WHERE deleted_at IS NULL;`);
    await query(`CREATE INDEX IF NOT EXISTS idx_invoices_tenant_id ON invoices(tenant_id) WHERE deleted_at IS NULL;`);
    await query(`CREATE INDEX IF NOT EXISTS idx_stock_items_tenant_id ON stock_items(tenant_id) WHERE deleted_at IS NULL;`);
    await query(`CREATE INDEX IF NOT EXISTS idx_payments_tenant_id ON payments(tenant_id) WHERE deleted_at IS NULL;`);
    await query(`CREATE INDEX IF NOT EXISTS idx_reminders_tenant_id ON reminders(tenant_id) WHERE deleted_at IS NULL;`);
    await query(`CREATE INDEX IF NOT EXISTS idx_messages_tenant_id ON messages(tenant_id) WHERE deleted_at IS NULL;`);
    await query(`CREATE INDEX IF NOT EXISTS idx_company_infos_tenant_id ON company_infos(tenant_id) WHERE deleted_at IS NULL;`);
    await query(`CREATE INDEX IF NOT EXISTS idx_user_payments_tenant_id ON user_payments(tenant_id) WHERE deleted_at IS NULL;`);
    await query(`CREATE INDEX IF NOT EXISTS idx_sms_settings_tenant_id ON sms_settings(tenant_id) WHERE deleted_at IS NULL;`);
    await query(`CREATE INDEX IF NOT EXISTS idx_service_steps_tenant_id ON service_steps(tenant_id) WHERE deleted_at IS NULL;`);
    await query(`CREATE INDEX IF NOT EXISTS idx_service_step_questions_tenant_id ON service_step_questions(tenant_id) WHERE deleted_at IS NULL;`);
    await query(`CREATE INDEX IF NOT EXISTS idx_device_brands_tenant_id ON device_brands(tenant_id) WHERE deleted_at IS NULL;`);
    await query(`CREATE INDEX IF NOT EXISTS idx_device_types_tenant_id ON device_types(tenant_id) WHERE deleted_at IS NULL;`);
    await query(`CREATE INDEX IF NOT EXISTS idx_service_sources_tenant_id ON service_sources(tenant_id) WHERE deleted_at IS NULL;`);
    await query(`CREATE INDEX IF NOT EXISTS idx_service_vehicles_tenant_id ON service_vehicles(tenant_id) WHERE deleted_at IS NULL;`);
    await query(`CREATE INDEX IF NOT EXISTS idx_customer_categories_tenant_id ON customer_categories(tenant_id) WHERE deleted_at IS NULL;`);
    await query(`CREATE INDEX IF NOT EXISTS idx_customer_custom_questions_tenant_id ON customer_custom_questions(tenant_id) WHERE deleted_at IS NULL;`);
    await query(`CREATE INDEX IF NOT EXISTS idx_personnel_positions_tenant_id ON personnel_positions(tenant_id) WHERE deleted_at IS NULL;`);
    await query(`CREATE INDEX IF NOT EXISTS idx_personnel_custom_questions_tenant_id ON personnel_custom_questions(tenant_id) WHERE deleted_at IS NULL;`);
    await query(`CREATE INDEX IF NOT EXISTS idx_stock_categories_tenant_id ON stock_categories(tenant_id) WHERE deleted_at IS NULL;`);
    await query(`CREATE INDEX IF NOT EXISTS idx_warehouses_tenant_id ON warehouses(tenant_id) WHERE deleted_at IS NULL;`);
    await query(`CREATE INDEX IF NOT EXISTS idx_suppliers_tenant_id ON suppliers(tenant_id) WHERE deleted_at IS NULL;`);
    await query(`CREATE INDEX IF NOT EXISTS idx_stock_custom_questions_tenant_id ON stock_custom_questions(tenant_id) WHERE deleted_at IS NULL;`);
    await query(`CREATE INDEX IF NOT EXISTS idx_services_tenant_id ON services(tenant_id) WHERE deleted_at IS NULL;`);
    await query(`CREATE INDEX IF NOT EXISTS idx_staff_tenant_id ON staff(tenant_id) WHERE deleted_at IS NULL;`);
    await query(`CREATE INDEX IF NOT EXISTS idx_cash_transactions_tenant_id ON cash_transactions(tenant_id) WHERE deleted_at IS NULL;`);

    // Soft delete indexes
    await query(`CREATE INDEX IF NOT EXISTS idx_tenants_active ON tenants(is_active) WHERE deleted_at IS NULL;`);
    await query(`CREATE INDEX IF NOT EXISTS idx_users_active ON users(is_active, tenant_id) WHERE deleted_at IS NULL;`);

    // Foreign key indexes for better join performance
    await query(`CREATE INDEX IF NOT EXISTS idx_devices_customer_id ON devices(customer_id);`);
    await query(`CREATE INDEX IF NOT EXISTS idx_service_requests_device_id ON service_requests(device_id);`);
    await query(`CREATE INDEX IF NOT EXISTS idx_service_requests_assigned_to ON service_requests(assigned_to);`);
    await query(`CREATE INDEX IF NOT EXISTS idx_service_notes_service_request_id ON service_notes(service_request_id);`);
    await query(`CREATE INDEX IF NOT EXISTS idx_invoices_service_request_id ON invoices(service_request_id);`);
    await query(`CREATE INDEX IF NOT EXISTS idx_payments_invoice_id ON payments(invoice_id);`);
    await query(`CREATE INDEX IF NOT EXISTS idx_reminders_customer_id ON reminders(customer_id);`);
    await query(`CREATE INDEX IF NOT EXISTS idx_messages_from_user ON messages(from_user_id);`);
    await query(`CREATE INDEX IF NOT EXISTS idx_messages_to_user ON messages(to_user_id);`);
    await query(`CREATE INDEX IF NOT EXISTS idx_company_infos_user_id ON company_infos(user_id);`);
    await query(`CREATE INDEX IF NOT EXISTS idx_user_payments_user_id ON user_payments(user_id);`);
    await query(`CREATE INDEX IF NOT EXISTS idx_sms_settings_user_id ON sms_settings(user_id);`);
    await query(`CREATE INDEX IF NOT EXISTS idx_service_steps_user_id ON service_steps(user_id);`);
    await query(`CREATE INDEX IF NOT EXISTS idx_service_step_questions_user_id ON service_step_questions(user_id);`);
    await query(`CREATE INDEX IF NOT EXISTS idx_service_step_questions_step_id ON service_step_questions(step_id);`);
    await query(`CREATE INDEX IF NOT EXISTS idx_device_brands_user_id ON device_brands(user_id);`);
    await query(`CREATE INDEX IF NOT EXISTS idx_device_types_user_id ON device_types(user_id);`);
    await query(`CREATE INDEX IF NOT EXISTS idx_service_sources_user_id ON service_sources(user_id);`);
    await query(`CREATE INDEX IF NOT EXISTS idx_service_vehicles_user_id ON service_vehicles(user_id);`);
    await query(`CREATE INDEX IF NOT EXISTS idx_customer_categories_user_id ON customer_categories(user_id);`);
    await query(`CREATE INDEX IF NOT EXISTS idx_customer_categories_parent_id ON customer_categories(parent_id);`);
    await query(`CREATE INDEX IF NOT EXISTS idx_customer_custom_questions_user_id ON customer_custom_questions(user_id);`);
    await query(`CREATE INDEX IF NOT EXISTS idx_personnel_positions_user_id ON personnel_positions(user_id);`);
    await query(`CREATE INDEX IF NOT EXISTS idx_personnel_custom_questions_user_id ON personnel_custom_questions(user_id);`);
    await query(`CREATE INDEX IF NOT EXISTS idx_stock_categories_user_id ON stock_categories(user_id);`);
    await query(`CREATE INDEX IF NOT EXISTS idx_stock_categories_parent_id ON stock_categories(parent_id);`);
    await query(`CREATE INDEX IF NOT EXISTS idx_warehouses_user_id ON warehouses(user_id);`);
    await query(`CREATE INDEX IF NOT EXISTS idx_warehouses_parent_id ON warehouses(parent_id);`);
    await query(`CREATE INDEX IF NOT EXISTS idx_suppliers_user_id ON suppliers(user_id);`);
    await query(`CREATE INDEX IF NOT EXISTS idx_stock_custom_questions_user_id ON stock_custom_questions(user_id);`);
    await query(`CREATE INDEX IF NOT EXISTS idx_services_user_id ON services(user_id);`);
    await query(`CREATE INDEX IF NOT EXISTS idx_services_customer_id ON services(customer_id);`);
    await query(`CREATE INDEX IF NOT EXISTS idx_services_assigned_to ON services(assigned_to);`);
    await query(`CREATE INDEX IF NOT EXISTS idx_staff_user_id ON staff(user_id);`);
    await query(`CREATE INDEX IF NOT EXISTS idx_cash_transactions_user_id ON cash_transactions(user_id);`);

    // Search and filter indexes
    await query(`CREATE INDEX IF NOT EXISTS idx_customers_search ON customers USING gin(to_tsvector('english', name || ' ' || phone || ' ' || COALESCE(email, '')));`);
    await query(`CREATE INDEX IF NOT EXISTS idx_service_requests_status ON service_requests(status, tenant_id);`);
    await query(`CREATE INDEX IF NOT EXISTS idx_invoices_paid ON invoices(paid, tenant_id);`);
    await query(`CREATE INDEX IF NOT EXISTS idx_reminders_date ON reminders(remind_date, tenant_id);`);
    await query(`CREATE INDEX IF NOT EXISTS idx_messages_unread ON messages(read, to_user_id, tenant_id);`);

    // Date-based indexes for reporting
    await query(`CREATE INDEX IF NOT EXISTS idx_service_requests_created_at ON service_requests(created_at, tenant_id);`);
    await query(`CREATE INDEX IF NOT EXISTS idx_invoices_issue_date ON invoices(issue_date, tenant_id);`);
    await query(`CREATE INDEX IF NOT EXISTS idx_payments_paid_at ON payments(paid_at, tenant_id);`);

    // New module specific indexes
    await query(`CREATE INDEX IF NOT EXISTS idx_user_payments_approval_status ON user_payments(approval_status, tenant_id);`);
    await query(`CREATE INDEX IF NOT EXISTS idx_user_payments_date_range ON user_payments(start_date, end_date, tenant_id);`);

    // Service definitions specific indexes
    await query(`CREATE INDEX IF NOT EXISTS idx_service_steps_order ON service_steps(step_order, user_id, tenant_id);`);
    await query(`CREATE INDEX IF NOT EXISTS idx_service_step_questions_order ON service_step_questions(question_order, step_id, tenant_id);`);

    // Dashboard and analytics indexes
    await query(`CREATE INDEX IF NOT EXISTS idx_services_status ON services(status, tenant_id) WHERE deleted_at IS NULL;`);
    await query(`CREATE INDEX IF NOT EXISTS idx_services_type ON services(service_type, tenant_id) WHERE deleted_at IS NULL;`);
    await query(`CREATE INDEX IF NOT EXISTS idx_services_created_at ON services(created_at, tenant_id) WHERE deleted_at IS NULL;`);
    await query(`CREATE INDEX IF NOT EXISTS idx_services_completed_at ON services(completed_at, tenant_id) WHERE deleted_at IS NULL;`);
    await query(`CREATE INDEX IF NOT EXISTS idx_staff_active ON staff(is_active, tenant_id) WHERE deleted_at IS NULL;`);
    await query(`CREATE INDEX IF NOT EXISTS idx_cash_transactions_type ON cash_transactions(transaction_type, tenant_id) WHERE deleted_at IS NULL;`);
    await query(`CREATE INDEX IF NOT EXISTS idx_cash_transactions_date ON cash_transactions(transaction_date, tenant_id) WHERE deleted_at IS NULL;`);
    await query(`CREATE INDEX IF NOT EXISTS idx_cash_transactions_category ON cash_transactions(category, tenant_id) WHERE deleted_at IS NULL;`);

    // Create utility functions
    console.log('Creating utility functions...');

    // Function to generate invoice numbers
    await query(`
      CREATE OR REPLACE FUNCTION generate_invoice_number(tenant_uuid UUID)
      RETURNS VARCHAR(50) AS $$
      DECLARE
        year_part VARCHAR(4);
        sequence_num INTEGER;
        invoice_num VARCHAR(50);
      BEGIN
        year_part := EXTRACT(YEAR FROM CURRENT_DATE)::VARCHAR;

        SELECT COALESCE(MAX(CAST(SUBSTRING(invoice_number FROM '[0-9]+$') AS INTEGER)), 0) + 1
        INTO sequence_num
        FROM invoices
        WHERE tenant_id = tenant_uuid
        AND invoice_number LIKE 'INV-' || year_part || '-%';

        invoice_num := 'INV-' || year_part || '-' || LPAD(sequence_num::VARCHAR, 6, '0');

        RETURN invoice_num;
      END;
      $$ LANGUAGE plpgsql;
    `);

    // Function to update timestamps
    await query(`
      CREATE OR REPLACE FUNCTION update_updated_at_column()
      RETURNS TRIGGER AS $$
      BEGIN
        NEW.updated_at = CURRENT_TIMESTAMP;
        RETURN NEW;
      END;
      $$ LANGUAGE plpgsql;
    `);

    // Create triggers for updated_at
    const tablesWithUpdatedAt = ['tenants', 'users', 'customers', 'devices', 'service_requests', 'invoices', 'stock_items', 'reminders', 'company_infos', 'user_payments', 'sms_settings', 'service_steps', 'service_step_questions', 'device_brands', 'device_types', 'service_sources', 'service_vehicles', 'customer_categories', 'customer_custom_questions', 'personnel_positions', 'personnel_custom_questions', 'stock_categories', 'warehouses', 'suppliers', 'stock_custom_questions', 'services', 'staff', 'cash_transactions'];
    for (const table of tablesWithUpdatedAt) {
      await query(`
        DROP TRIGGER IF EXISTS trigger_${table}_updated_at ON ${table};
        CREATE TRIGGER trigger_${table}_updated_at
        BEFORE UPDATE ON ${table}
        FOR EACH ROW
        EXECUTE FUNCTION update_updated_at_column();
      `);
    }

    console.log('✅ Database migration completed successfully');
  } catch (error) {
    console.error('❌ Database migration failed:', error);
    throw error;
  }
};

// Run migration if called directly
if (require.main === module) {
  createTables()
    .then(() => process.exit(0))
    .catch(() => process.exit(1));
}

export { createTables };
