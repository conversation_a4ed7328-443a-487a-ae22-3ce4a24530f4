import { query } from './connection';

const createTables = async () => {
  try {
    console.log('🔄 Starting database migration...');

    // Enable UUID extension
    await query(`CREATE EXTENSION IF NOT EXISTS "uuid-ossp";`);

    // Create Tenants table
    await query(`
      CREATE TABLE IF NOT EXISTS tenants (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        name VARCHAR(255) NOT NULL,
        slug VARCHAR(100) NOT NULL UNIQUE,
        logo VARCHAR(500),
        is_active BOOLEAN DEFAULT true,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `);

    // Create Users table
    await query(`
      CREATE TABLE IF NOT EXISTS users (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        name VARCHAR(255) NOT NULL,
        email VARCHAR(255) NOT NULL UNIQUE,
        password VARCHAR(255) NOT NULL,
        role VARCHAR(50) NOT NULL CHECK (role IN ('admin', 'technician', 'operator', 'viewer')),
        tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
        is_active BOOLEAN DEFAULT true,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `);

    // Create Customers table
    await query(`
      CREATE TABLE IF NOT EXISTS customers (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        name VARCHAR(255) NOT NULL,
        phone VARCHAR(50) NOT NULL,
        email VARCHAR(255),
        address TEXT,
        tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
        created_by UUID NOT NULL REFERENCES users(id),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `);

    // Create Devices table
    await query(`
      CREATE TABLE IF NOT EXISTS devices (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        customer_id UUID NOT NULL REFERENCES customers(id) ON DELETE CASCADE,
        brand VARCHAR(255) NOT NULL,
        model VARCHAR(255) NOT NULL,
        serial_number VARCHAR(255) UNIQUE,
        warranty_expiry DATE,
        tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `);

    // Create Service Requests table
    await query(`
      CREATE TABLE IF NOT EXISTS service_requests (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        device_id UUID NOT NULL REFERENCES devices(id) ON DELETE CASCADE,
        status VARCHAR(50) NOT NULL DEFAULT 'Pending' CHECK (status IN ('Pending', 'Assigned', 'OnTheWay', 'InRepair', 'Completed')),
        description TEXT NOT NULL,
        assigned_to UUID REFERENCES users(id),
        request_date DATE DEFAULT CURRENT_DATE,
        estimated_completion DATE,
        tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `);

    // Create Service Notes table
    await query(`
      CREATE TABLE IF NOT EXISTS service_notes (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        service_request_id UUID NOT NULL REFERENCES service_requests(id) ON DELETE CASCADE,
        note TEXT NOT NULL,
        photo VARCHAR(500),
        created_by UUID NOT NULL REFERENCES users(id),
        tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `);

    // Create Invoices table
    await query(`
      CREATE TABLE IF NOT EXISTS invoices (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        service_request_id UUID NOT NULL REFERENCES service_requests(id) ON DELETE CASCADE,
        amount DECIMAL(10,2) NOT NULL,
        paid BOOLEAN DEFAULT false,
        issue_date DATE DEFAULT CURRENT_DATE,
        due_date DATE,
        invoice_pdf VARCHAR(500),
        tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `);

    // Create Stock Items table
    await query(`
      CREATE TABLE IF NOT EXISTS stock_items (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        name VARCHAR(255) NOT NULL,
        code VARCHAR(100) UNIQUE,
        quantity INTEGER DEFAULT 0,
        minimum_threshold INTEGER,
        price DECIMAL(10,2),
        tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `);

    // Create Payments table
    await query(`
      CREATE TABLE IF NOT EXISTS payments (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        invoice_id UUID NOT NULL REFERENCES invoices(id) ON DELETE CASCADE,
        amount DECIMAL(10,2) NOT NULL,
        payment_method VARCHAR(50) NOT NULL CHECK (payment_method IN ('Cash', 'Card', 'Online')),
        paid_by VARCHAR(255),
        paid_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE
      );
    `);

    // Create Reminders table
    await query(`
      CREATE TABLE IF NOT EXISTS reminders (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        customer_id UUID NOT NULL REFERENCES customers(id) ON DELETE CASCADE,
        title VARCHAR(255) NOT NULL,
        remind_date DATE NOT NULL,
        is_sent BOOLEAN DEFAULT false,
        tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `);

    // Create Messages table
    await query(`
      CREATE TABLE IF NOT EXISTS messages (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        from_user_id UUID NOT NULL REFERENCES users(id),
        to_user_id UUID NOT NULL REFERENCES users(id),
        content TEXT NOT NULL,
        read BOOLEAN DEFAULT false,
        tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `);

    // Create indexes for better performance
    await query(`CREATE INDEX IF NOT EXISTS idx_users_tenant_id ON users(tenant_id);`);
    await query(`CREATE INDEX IF NOT EXISTS idx_customers_tenant_id ON customers(tenant_id);`);
    await query(`CREATE INDEX IF NOT EXISTS idx_devices_tenant_id ON devices(tenant_id);`);
    await query(`CREATE INDEX IF NOT EXISTS idx_service_requests_tenant_id ON service_requests(tenant_id);`);
    await query(`CREATE INDEX IF NOT EXISTS idx_service_notes_tenant_id ON service_notes(tenant_id);`);
    await query(`CREATE INDEX IF NOT EXISTS idx_invoices_tenant_id ON invoices(tenant_id);`);
    await query(`CREATE INDEX IF NOT EXISTS idx_stock_items_tenant_id ON stock_items(tenant_id);`);
    await query(`CREATE INDEX IF NOT EXISTS idx_payments_tenant_id ON payments(tenant_id);`);
    await query(`CREATE INDEX IF NOT EXISTS idx_reminders_tenant_id ON reminders(tenant_id);`);
    await query(`CREATE INDEX IF NOT EXISTS idx_messages_tenant_id ON messages(tenant_id);`);

    console.log('✅ Database migration completed successfully');
  } catch (error) {
    console.error('❌ Database migration failed:', error);
    throw error;
  }
};

// Run migration if called directly
if (require.main === module) {
  createTables()
    .then(() => process.exit(0))
    .catch(() => process.exit(1));
}

export { createTables };
