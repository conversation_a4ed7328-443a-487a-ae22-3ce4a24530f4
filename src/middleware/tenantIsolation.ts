import { Request, Response, NextFunction } from 'express';
import { AuthenticatedRequest } from './auth';

export interface TenantRequest extends AuthenticatedRequest {
  tenantId?: string;
}

export const tenantIsolation = (req: TenantRequest, res: Response, next: NextFunction) => {
  if (!req.user) {
    return res.status(401).json({
      success: false,
      error: 'Authentication required for tenant isolation'
    });
  }

  // Add tenant ID to request for easy access
  req.tenantId = req.user.tenantId;
  next();
};

// Database query helper that automatically adds tenant filtering
export class TenantAwareQuery {
  private tenantId: string;

  constructor(tenantId: string) {
    this.tenantId = tenantId;
  }

  // Add tenant filter to WHERE clause
  addTenantFilter(baseQuery: string, params: any[] = []): { query: string; params: any[] } {
    const tenantCondition = params.length === 0 
      ? 'WHERE tenant_id = $1' 
      : 'AND tenant_id = $' + (params.length + 1);
    
    const query = baseQuery.includes('WHERE') 
      ? baseQuery + ' ' + tenantCondition.replace('WHERE', 'AND')
      : baseQuery + ' ' + tenantCondition;
    
    return {
      query,
      params: [...params, this.tenantId]
    };
  }

  // For INSERT queries, automatically add tenant_id
  addTenantToInsert(columns: string[], values: any[]): { columns: string[]; values: any[] } {
    if (!columns.includes('tenant_id')) {
      columns.push('tenant_id');
      values.push(this.tenantId);
    }
    return { columns, values };
  }

  // Generate parameterized placeholders
  generatePlaceholders(count: number, startIndex: number = 1): string {
    return Array.from({ length: count }, (_, i) => `$${startIndex + i}`).join(', ');
  }
}

// Helper function to create tenant-aware query instance
export const createTenantQuery = (req: TenantRequest): TenantAwareQuery => {
  if (!req.tenantId) {
    throw new Error('Tenant ID not found in request');
  }
  return new TenantAwareQuery(req.tenantId);
};
