import { Request, Response, NextFunction } from 'express';
import { AuthenticatedRequest } from './auth';

export type Permission = 
  | 'tenant:read' | 'tenant:write' | 'tenant:delete'
  | 'user:read' | 'user:write' | 'user:delete'
  | 'customer:read' | 'customer:write' | 'customer:delete'
  | 'device:read' | 'device:write' | 'device:delete'
  | 'service_request:read' | 'service_request:write' | 'service_request:delete'
  | 'service_note:read' | 'service_note:write' | 'service_note:delete'
  | 'invoice:read' | 'invoice:write' | 'invoice:delete'
  | 'stock_item:read' | 'stock_item:write' | 'stock_item:delete'
  | 'payment:read' | 'payment:write' | 'payment:delete'
  | 'reminder:read' | 'reminder:write' | 'reminder:delete'
  | 'message:read' | 'message:write' | 'message:delete';

export type Role = 'admin' | 'technician' | 'operator' | 'viewer';

// Define permissions for each role
export const rolePermissions: Record<Role, Permission[]> = {
  admin: [
    // Full access to everything
    'tenant:read', 'tenant:write', 'tenant:delete',
    'user:read', 'user:write', 'user:delete',
    'customer:read', 'customer:write', 'customer:delete',
    'device:read', 'device:write', 'device:delete',
    'service_request:read', 'service_request:write', 'service_request:delete',
    'service_note:read', 'service_note:write', 'service_note:delete',
    'invoice:read', 'invoice:write', 'invoice:delete',
    'stock_item:read', 'stock_item:write', 'stock_item:delete',
    'payment:read', 'payment:write', 'payment:delete',
    'reminder:read', 'reminder:write', 'reminder:delete',
    'message:read', 'message:write', 'message:delete',
  ],
  
  technician: [
    // Can only work with assigned service requests and related data
    'service_request:read', 'service_request:write',
    'service_note:read', 'service_note:write',
    'customer:read', 'device:read',
    'stock_item:read',
    'message:read', 'message:write',
  ],
  
  operator: [
    // CRUD on customers, devices, and requests
    'customer:read', 'customer:write', 'customer:delete',
    'device:read', 'device:write', 'device:delete',
    'service_request:read', 'service_request:write', 'service_request:delete',
    'service_note:read',
    'invoice:read', 'invoice:write',
    'stock_item:read', 'stock_item:write',
    'payment:read', 'payment:write',
    'reminder:read', 'reminder:write', 'reminder:delete',
    'message:read', 'message:write',
  ],
  
  viewer: [
    // Read-only access
    'customer:read', 'device:read',
    'service_request:read', 'service_note:read',
    'invoice:read', 'stock_item:read',
    'payment:read', 'reminder:read',
    'message:read',
  ],
};

export const hasPermission = (role: Role, permission: Permission): boolean => {
  return rolePermissions[role].includes(permission);
};

export const requirePermission = (permission: Permission) => {
  return (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required'
      });
    }

    const userRole = req.user.role as Role;
    
    if (!hasPermission(userRole, permission)) {
      return res.status(403).json({
        success: false,
        error: `Insufficient permissions. Required: ${permission}`
      });
    }

    next();
  };
};

// Special middleware for technicians - they can only access their assigned service requests
export const technicianResourceFilter = (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  if (!req.user) {
    return res.status(401).json({
      success: false,
      error: 'Authentication required'
    });
  }

  // If user is a technician, add filter for assigned_to
  if (req.user.role === 'technician') {
    req.query.assigned_to = req.user.userId;
  }

  next();
};
