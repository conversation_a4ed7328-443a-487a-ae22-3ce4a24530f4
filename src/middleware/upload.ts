import multer from 'multer';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';
import fs from 'fs';

// Ensure upload directory exists
const uploadDir = process.env.UPLOAD_PATH || './uploads';
if (!fs.existsSync(uploadDir)) {
  fs.mkdirSync(uploadDir, { recursive: true });
}

// Create subdirectories for different file types
const subdirs = ['photos', 'invoices', 'logos'];
subdirs.forEach(subdir => {
  const fullPath = path.join(uploadDir, subdir);
  if (!fs.existsSync(fullPath)) {
    fs.mkdirSync(fullPath, { recursive: true });
  }
});

// Configure storage
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    let subdir = 'photos'; // default
    
    if (file.fieldname === 'invoice_pdf') {
      subdir = 'invoices';
    } else if (file.fieldname === 'logo') {
      subdir = 'logos';
    } else if (file.fieldname === 'photo') {
      subdir = 'photos';
    }
    
    cb(null, path.join(uploadDir, subdir));
  },
  filename: (req, file, cb) => {
    // Generate unique filename with original extension
    const uniqueName = `${uuidv4()}${path.extname(file.originalname)}`;
    cb(null, uniqueName);
  }
});

// File filter function
const fileFilter = (req: any, file: Express.Multer.File, cb: multer.FileFilterCallback) => {
  // Define allowed file types for different fields
  const allowedTypes: { [key: string]: string[] } = {
    photo: ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'],
    invoice_pdf: ['application/pdf'],
    logo: ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/svg+xml']
  };

  const fieldAllowedTypes = allowedTypes[file.fieldname] || allowedTypes.photo;
  
  if (fieldAllowedTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new Error(`Invalid file type for ${file.fieldname}. Allowed types: ${fieldAllowedTypes.join(', ')}`));
  }
};

// Configure multer
const upload = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: parseInt(process.env.MAX_FILE_SIZE || '10485760'), // 10MB default
    files: 5 // Maximum 5 files per request
  }
});

// Middleware for different upload scenarios
export const uploadPhoto = upload.single('photo');
export const uploadInvoicePDF = upload.single('invoice_pdf');
export const uploadLogo = upload.single('logo');
export const uploadMultiple = upload.array('files', 5);

// Error handling middleware for multer
export const handleUploadError = (error: any, req: any, res: any, next: any) => {
  if (error instanceof multer.MulterError) {
    if (error.code === 'LIMIT_FILE_SIZE') {
      return res.status(400).json({
        success: false,
        error: 'File too large. Maximum size is 10MB.'
      });
    }
    if (error.code === 'LIMIT_FILE_COUNT') {
      return res.status(400).json({
        success: false,
        error: 'Too many files. Maximum 5 files allowed.'
      });
    }
    if (error.code === 'LIMIT_UNEXPECTED_FILE') {
      return res.status(400).json({
        success: false,
        error: 'Unexpected file field.'
      });
    }
  }
  
  if (error.message.includes('Invalid file type')) {
    return res.status(400).json({
      success: false,
      error: error.message
    });
  }
  
  next(error);
};

// Utility function to delete uploaded file
export const deleteFile = (filePath: string): void => {
  try {
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
    }
  } catch (error) {
    console.error('Error deleting file:', error);
  }
};

// Utility function to get file URL
export const getFileUrl = (filename: string, type: 'photos' | 'invoices' | 'logos' = 'photos'): string => {
  return `/uploads/${type}/${filename}`;
};
