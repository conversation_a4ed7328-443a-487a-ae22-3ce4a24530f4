export interface Tenant {
  id: string;
  name: string;
  slug: string;
  logo?: string;
  is_active: boolean;
  deleted_at?: Date;
  created_at: Date;
  updated_at: Date;
}

export interface User {
  id: string;
  name: string;
  email: string;
  password: string;
  role: 'admin' | 'technician' | 'operator' | 'viewer';
  tenant_id: string;
  is_active: boolean;
  deleted_at?: Date;
  last_login?: Date;
  created_at: Date;
  updated_at: Date;
}

export interface Customer {
  id: string;
  name: string;
  phone: string;
  email?: string;
  address?: string;
  tenant_id: string;
  created_by: string;
  created_at: Date;
  updated_at: Date;
}

export interface Device {
  id: string;
  customer_id: string;
  brand: string;
  model: string;
  serial_number?: string;
  warranty_expiry?: Date;
  tenant_id: string;
  created_at: Date;
  updated_at: Date;
}

export interface ServiceRequest {
  id: string;
  device_id: string;
  status: 'Pending' | 'Assigned' | 'OnTheWay' | 'InRepair' | 'Completed';
  description: string;
  assigned_to?: string;
  request_date: Date;
  estimated_completion?: Date;
  tenant_id: string;
  created_at: Date;
  updated_at: Date;
}

export interface ServiceNote {
  id: string;
  service_request_id: string;
  note: string;
  photo?: string;
  created_by: string;
  tenant_id: string;
  created_at: Date;
}

export interface Invoice {
  id: string;
  service_request_id: string;
  amount: number;
  paid: boolean;
  issue_date: Date;
  due_date?: Date;
  invoice_pdf?: string;
  tenant_id: string;
  created_at: Date;
  updated_at: Date;
}

export interface StockItem {
  id: string;
  name: string;
  code?: string;
  quantity: number;
  minimum_threshold?: number;
  price?: number;
  tenant_id: string;
  created_at: Date;
  updated_at: Date;
}

export interface Payment {
  id: string;
  invoice_id: string;
  amount: number;
  payment_method: 'Cash' | 'Card' | 'Online';
  paid_by?: string;
  paid_at: Date;
  tenant_id: string;
}

export interface Reminder {
  id: string;
  customer_id: string;
  title: string;
  remind_date: Date;
  is_sent: boolean;
  tenant_id: string;
  created_at: Date;
  updated_at: Date;
}

export interface Message {
  id: string;
  from_user_id: string;
  to_user_id: string;
  content: string;
  read: boolean;
  tenant_id: string;
  created_at: Date;
}

export interface CompanyInfo {
  id: string;
  user_id: string;
  company_name: string;
  phone1: string;
  phone2?: string;
  city: string;
  district: string;
  address: string;
  email?: string;
  website?: string;
  tax_number?: string;
  tax_office?: string;
  tenant_id: string;
  deleted_at?: Date;
  created_at: Date;
  updated_at: Date;
}

export interface UserPayment {
  id: string;
  user_id: string;
  start_date: Date;
  end_date: Date;
  price: number;
  approval_status: 'onaylandı' | 'beklemede' | 'reddedildi';
  tenant_id: string;
  deleted_at?: Date;
  created_at: Date;
  updated_at: Date;
}

export interface SmsSettings {
  id: string;
  user_id: string;
  sender_company: string;
  message_header: string;
  username: string;
  password: string;
  sms_template: string;
  tenant_id: string;
  deleted_at?: Date;
  created_at: Date;
  updated_at: Date;
}

// Request/Response types
export interface CreateTenantRequest {
  name: string;
  slug: string;
  logo?: string;
}

export interface CreateUserRequest {
  name: string;
  email: string;
  password: string;
  role: 'admin' | 'technician' | 'operator' | 'viewer';
}

export interface LoginRequest {
  email: string;
  password: string;
}

export interface AuthResponse {
  token: string;
  user: Omit<User, 'password'>;
  tenant: Tenant;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

// New module request types
export interface CreateCompanyInfoRequest {
  company_name: string;
  phone1: string;
  phone2?: string;
  city: string;
  district: string;
  address: string;
  email?: string;
  website?: string;
  tax_number?: string;
  tax_office?: string;
}

export interface UpdateCompanyInfoRequest extends Partial<CreateCompanyInfoRequest> {}

export interface CreateUserPaymentRequest {
  start_date: string; // ISO date string
  end_date: string; // ISO date string
  price: number;
  approval_status?: 'onaylandı' | 'beklemede' | 'reddedildi';
}

export interface CreateSmsSettingsRequest {
  sender_company: string;
  message_header: string;
  username: string;
  password: string;
  sms_template: string;
}

export interface UpdateSmsSettingsRequest extends Partial<CreateSmsSettingsRequest> {}

// Service Definitions Models
export interface ServiceStep {
  id: string;
  user_id: string;
  name: string;
  step_order: number;
  tenant_id: string;
  deleted_at?: Date;
  created_at: Date;
  updated_at: Date;
}

export interface ServiceStepQuestion {
  id: string;
  user_id: string;
  step_id: string;
  question_text: string;
  answer_type: 'Açıklama' | 'Tarih' | 'Seçim' | 'Sayı' | 'Evet/Hayır';
  question_order: number;
  tenant_id: string;
  deleted_at?: Date;
  created_at: Date;
  updated_at: Date;
}

export interface DeviceBrand {
  id: string;
  user_id: string;
  name: string;
  tenant_id: string;
  deleted_at?: Date;
  created_at: Date;
  updated_at: Date;
}

export interface DeviceType {
  id: string;
  user_id: string;
  name: string;
  tenant_id: string;
  deleted_at?: Date;
  created_at: Date;
  updated_at: Date;
}

export interface ServiceSource {
  id: string;
  user_id: string;
  name: string;
  tenant_id: string;
  deleted_at?: Date;
  created_at: Date;
  updated_at: Date;
}

export interface ServiceVehicle {
  id: string;
  user_id: string;
  plate_number: string;
  tenant_id: string;
  deleted_at?: Date;
  created_at: Date;
  updated_at: Date;
}

// Service Definitions Request Types
export interface CreateServiceStepRequest {
  name: string;
  step_order: number;
}

export interface UpdateServiceStepRequest extends Partial<CreateServiceStepRequest> {}

export interface CreateServiceStepQuestionRequest {
  step_id: string;
  question_text: string;
  answer_type: 'Açıklama' | 'Tarih' | 'Seçim' | 'Sayı' | 'Evet/Hayır';
  question_order: number;
}

export interface UpdateServiceStepQuestionRequest extends Partial<Omit<CreateServiceStepQuestionRequest, 'step_id'>> {}

export interface CreateDeviceBrandRequest {
  name: string;
}

export interface UpdateDeviceBrandRequest extends Partial<CreateDeviceBrandRequest> {}

export interface CreateDeviceTypeRequest {
  name: string;
}

export interface UpdateDeviceTypeRequest extends Partial<CreateDeviceTypeRequest> {}

export interface CreateServiceSourceRequest {
  name: string;
}

export interface UpdateServiceSourceRequest extends Partial<CreateServiceSourceRequest> {}

export interface CreateServiceVehicleRequest {
  plate_number: string;
}

export interface UpdateServiceVehicleRequest extends Partial<CreateServiceVehicleRequest> {}

// Customer & Personnel Models
export interface CustomerCategory {
  id: string;
  user_id: string;
  name: string;
  parent_id?: string;
  tenant_id: string;
  deleted_at?: Date;
  created_at: Date;
  updated_at: Date;
}

export interface CustomerCustomQuestion {
  id: string;
  user_id: string;
  question_text: string;
  tenant_id: string;
  deleted_at?: Date;
  created_at: Date;
  updated_at: Date;
}

export interface PersonnelPosition {
  id: string;
  user_id: string;
  name: string;
  tenant_id: string;
  deleted_at?: Date;
  created_at: Date;
  updated_at: Date;
}

export interface PersonnelCustomQuestion {
  id: string;
  user_id: string;
  question_text: string;
  tenant_id: string;
  deleted_at?: Date;
  created_at: Date;
  updated_at: Date;
}

// Customer & Personnel Request Types
export interface CreateCustomerCategoryRequest {
  name: string;
  parent_id?: string;
}

export interface UpdateCustomerCategoryRequest extends Partial<CreateCustomerCategoryRequest> {}

export interface CreateCustomerCustomQuestionRequest {
  question_text: string;
}

export interface UpdateCustomerCustomQuestionRequest extends Partial<CreateCustomerCustomQuestionRequest> {}

export interface CreatePersonnelPositionRequest {
  name: string;
}

export interface UpdatePersonnelPositionRequest extends Partial<CreatePersonnelPositionRequest> {}

export interface CreatePersonnelCustomQuestionRequest {
  question_text: string;
}

export interface UpdatePersonnelCustomQuestionRequest extends Partial<CreatePersonnelCustomQuestionRequest> {}
