import { query } from '../database/connection';
import { StockCategory, CreateStockCategoryRequest, UpdateStockCategoryRequest, PaginatedResponse } from '../types/models';

export class StockCategoryService {
  async getAll(
    userId: string,
    tenantId: string,
    page: number = 1,
    limit: number = 50
  ): Promise<PaginatedResponse<StockCategory>> {
    const offset = (page - 1) * limit;

    // Get total count
    const countSql = 'SELECT COUNT(*) FROM stock_categories WHERE user_id = $1 AND tenant_id = $2 AND deleted_at IS NULL';
    const countResult = await query(countSql, [userId, tenantId]);
    const total = parseInt(countResult.rows[0].count);

    // Get paginated results
    const dataSql = `SELECT * FROM stock_categories 
                     WHERE user_id = $1 AND tenant_id = $2 AND deleted_at IS NULL 
                     ORDER BY name ASC 
                     LIMIT $3 OFFSET $4`;
    const result = await query(dataSql, [userId, tenantId, limit, offset]);

    return {
      data: result.rows,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  async getById(id: string, userId: string, tenantId: string): Promise<StockCategory | null> {
    const sql = 'SELECT * FROM stock_categories WHERE id = $1 AND user_id = $2 AND tenant_id = $3 AND deleted_at IS NULL';
    const result = await query(sql, [id, userId, tenantId]);
    return result.rows.length > 0 ? result.rows[0] : null;
  }

  async getHierarchy(userId: string, tenantId: string): Promise<StockCategory[]> {
    const sql = `
      WITH RECURSIVE category_tree AS (
        -- Base case: root categories (no parent)
        SELECT id, user_id, name, parent_id, tenant_id, deleted_at, created_at, updated_at, 0 as level
        FROM stock_categories
        WHERE parent_id IS NULL AND user_id = $1 AND tenant_id = $2 AND deleted_at IS NULL
        
        UNION ALL
        
        -- Recursive case: child categories
        SELECT c.id, c.user_id, c.name, c.parent_id, c.tenant_id, c.deleted_at, c.created_at, c.updated_at, ct.level + 1
        FROM stock_categories c
        INNER JOIN category_tree ct ON c.parent_id = ct.id
        WHERE c.deleted_at IS NULL
      )
      SELECT * FROM category_tree
      ORDER BY level, name
    `;
    
    const result = await query(sql, [userId, tenantId]);
    return result.rows;
  }

  async getChildren(parentId: string, userId: string, tenantId: string): Promise<StockCategory[]> {
    const sql = `SELECT * FROM stock_categories 
                 WHERE parent_id = $1 AND user_id = $2 AND tenant_id = $3 AND deleted_at IS NULL 
                 ORDER BY name ASC`;
    const result = await query(sql, [parentId, userId, tenantId]);
    return result.rows;
  }

  async create(
    data: CreateStockCategoryRequest,
    userId: string,
    tenantId: string
  ): Promise<StockCategory> {
    const { name, parent_id } = data;

    // Check for duplicate name at the same level
    let duplicateCheckSql;
    let duplicateCheckParams;
    
    if (parent_id) {
      // Verify parent exists and belongs to user
      const parentCheck = await query(
        'SELECT id FROM stock_categories WHERE id = $1 AND user_id = $2 AND tenant_id = $3 AND deleted_at IS NULL',
        [parent_id, userId, tenantId]
      );
      
      if (parentCheck.rows.length === 0) {
        throw new Error('Parent category not found');
      }

      duplicateCheckSql = 'SELECT id FROM stock_categories WHERE user_id = $1 AND tenant_id = $2 AND name = $3 AND parent_id = $4 AND deleted_at IS NULL';
      duplicateCheckParams = [userId, tenantId, name, parent_id];
    } else {
      duplicateCheckSql = 'SELECT id FROM stock_categories WHERE user_id = $1 AND tenant_id = $2 AND name = $3 AND parent_id IS NULL AND deleted_at IS NULL';
      duplicateCheckParams = [userId, tenantId, name];
    }

    const existingCategory = await query(duplicateCheckSql, duplicateCheckParams);
    if (existingCategory.rows.length > 0) {
      throw new Error('Category with this name already exists at this level');
    }

    const sql = `
      INSERT INTO stock_categories (user_id, name, parent_id, tenant_id)
      VALUES ($1, $2, $3, $4)
      RETURNING *
    `;

    const result = await query(sql, [userId, name, parent_id || null, tenantId]);
    return result.rows[0];
  }

  async update(
    id: string,
    data: UpdateStockCategoryRequest,
    userId: string,
    tenantId: string
  ): Promise<StockCategory> {
    const existingCategory = await this.getById(id, userId, tenantId);
    if (!existingCategory) {
      throw new Error('Category not found');
    }

    // Prevent circular references
    if (data.parent_id) {
      if (data.parent_id === id) {
        throw new Error('Category cannot be its own parent');
      }

      // Check if the new parent would create a circular reference
      const isDescendant = await this.isDescendant(id, data.parent_id, userId, tenantId);
      if (isDescendant) {
        throw new Error('Cannot move category to its own descendant');
      }

      // Verify parent exists
      const parentCheck = await query(
        'SELECT id FROM stock_categories WHERE id = $1 AND user_id = $2 AND tenant_id = $3 AND deleted_at IS NULL',
        [data.parent_id, userId, tenantId]
      );
      
      if (parentCheck.rows.length === 0) {
        throw new Error('Parent category not found');
      }
    }

    const setParts: string[] = [];
    const values: any[] = [];
    let paramIndex = 1;

    // Build dynamic update query
    Object.entries(data).forEach(([key, value]) => {
      if (value !== undefined) {
        setParts.push(`${key} = $${paramIndex++}`);
        values.push(value);
      }
    });

    if (setParts.length === 0) {
      throw new Error('No fields to update');
    }

    setParts.push(`updated_at = CURRENT_TIMESTAMP`);
    values.push(id, userId, tenantId);

    const sql = `UPDATE stock_categories SET ${setParts.join(', ')} 
                 WHERE id = $${paramIndex} AND user_id = $${paramIndex + 1} AND tenant_id = $${paramIndex + 2} AND deleted_at IS NULL 
                 RETURNING *`;

    const result = await query(sql, values);

    if (result.rows.length === 0) {
      throw new Error('Category not found');
    }

    return result.rows[0];
  }

  async delete(id: string, userId: string, tenantId: string): Promise<void> {
    // Check if category has children
    const childrenResult = await query(
      'SELECT COUNT(*) FROM stock_categories WHERE parent_id = $1 AND deleted_at IS NULL',
      [id]
    );

    if (parseInt(childrenResult.rows[0].count) > 0) {
      throw new Error('Cannot delete category that has subcategories. Delete subcategories first.');
    }

    const sql = `UPDATE stock_categories 
                 SET deleted_at = CURRENT_TIMESTAMP 
                 WHERE id = $1 AND user_id = $2 AND tenant_id = $3 AND deleted_at IS NULL`;

    const result = await query(sql, [id, userId, tenantId]);

    if (result.rowCount === 0) {
      throw new Error('Category not found');
    }
  }

  private async isDescendant(ancestorId: string, descendantId: string, userId: string, tenantId: string): Promise<boolean> {
    const sql = `
      WITH RECURSIVE descendants AS (
        SELECT id, parent_id
        FROM stock_categories
        WHERE id = $1 AND user_id = $2 AND tenant_id = $3 AND deleted_at IS NULL
        
        UNION ALL
        
        SELECT c.id, c.parent_id
        FROM stock_categories c
        INNER JOIN descendants d ON c.parent_id = d.id
        WHERE c.deleted_at IS NULL
      )
      SELECT 1 FROM descendants WHERE id = $4
    `;
    
    const result = await query(sql, [ancestorId, userId, tenantId, descendantId]);
    return result.rows.length > 0;
  }

  async search(
    userId: string,
    tenantId: string,
    searchTerm: string,
    page: number = 1,
    limit: number = 50
  ): Promise<PaginatedResponse<StockCategory>> {
    const offset = (page - 1) * limit;

    // Get total count
    const countSql = `SELECT COUNT(*) FROM stock_categories 
                      WHERE user_id = $1 AND tenant_id = $2 AND deleted_at IS NULL 
                      AND name ILIKE $3`;
    const countResult = await query(countSql, [userId, tenantId, `%${searchTerm}%`]);
    const total = parseInt(countResult.rows[0].count);

    // Get paginated results
    const dataSql = `SELECT * FROM stock_categories 
                     WHERE user_id = $1 AND tenant_id = $2 AND deleted_at IS NULL 
                     AND name ILIKE $3
                     ORDER BY name ASC 
                     LIMIT $4 OFFSET $5`;
    const result = await query(dataSql, [userId, tenantId, `%${searchTerm}%`, limit, offset]);

    return {
      data: result.rows,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }
}
