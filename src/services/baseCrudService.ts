import { query } from '../database/connection';
import { PaginatedResponse } from '../types/models';

export abstract class BaseCrudService<T, CreateRequest extends Record<string, any>, UpdateRequest extends Record<string, any>> {
  protected tableName: string;
  protected entityName: string;

  constructor(tableName: string, entityName: string) {
    this.tableName = tableName;
    this.entityName = entityName;
  }

  async getAll(
    userId: string,
    tenantId: string,
    page: number = 1,
    limit: number = 50
  ): Promise<PaginatedResponse<T>> {
    const offset = (page - 1) * limit;

    // Get total count
    const countSql = `SELECT COUNT(*) FROM ${this.tableName} WHERE user_id = $1 AND tenant_id = $2 AND deleted_at IS NULL`;
    const countResult = await query(countSql, [userId, tenantId]);
    const total = parseInt(countResult.rows[0].count);

    // Get paginated results
    const dataSql = `SELECT * FROM ${this.tableName} 
                     WHERE user_id = $1 AND tenant_id = $2 AND deleted_at IS NULL 
                     ORDER BY created_at DESC 
                     LIMIT $3 OFFSET $4`;
    const result = await query(dataSql, [userId, tenantId, limit, offset]);

    return {
      data: result.rows,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  async getById(id: string, userId: string, tenantId: string): Promise<T | null> {
    const sql = `SELECT * FROM ${this.tableName} WHERE id = $1 AND user_id = $2 AND tenant_id = $3 AND deleted_at IS NULL`;
    const result = await query(sql, [id, userId, tenantId]);
    return result.rows.length > 0 ? result.rows[0] : null;
  }

  async create(
    data: CreateRequest,
    userId: string,
    tenantId: string,
    additionalFields: Record<string, any> = {}
  ): Promise<T> {
    // Check for duplicate name if applicable
    if ('name' in data) {
      const existingItem = await query(
        `SELECT id FROM ${this.tableName} WHERE user_id = $1 AND tenant_id = $2 AND name = $3 AND deleted_at IS NULL`,
        [userId, tenantId, (data as any).name]
      );

      if (existingItem.rows.length > 0) {
        throw new Error(`${this.entityName} with this name already exists`);
      }
    }

    const fields = ['user_id', 'tenant_id'];
    const values = [userId, tenantId];
    const placeholders = ['$1', '$2'];
    let paramIndex = 3;

    // Add data fields
    Object.entries(data as Record<string, any>).forEach(([key, value]) => {
      if (value !== undefined) {
        fields.push(key);
        values.push(value);
        placeholders.push(`$${paramIndex++}`);
      }
    });

    // Add additional fields
    Object.entries(additionalFields).forEach(([key, value]) => {
      if (value !== undefined) {
        fields.push(key);
        values.push(value);
        placeholders.push(`$${paramIndex++}`);
      }
    });

    const sql = `
      INSERT INTO ${this.tableName} (${fields.join(', ')})
      VALUES (${placeholders.join(', ')})
      RETURNING *
    `;

    const result = await query(sql, values);
    return result.rows[0];
  }

  async update(
    id: string,
    data: UpdateRequest,
    userId: string,
    tenantId: string
  ): Promise<T> {
    const existingItem = await this.getById(id, userId, tenantId);
    if (!existingItem) {
      throw new Error(`${this.entityName} not found`);
    }

    // Check for duplicate name if updating name
    if ('name' in data && (data as any).name) {
      const existingItem = await query(
        `SELECT id FROM ${this.tableName} WHERE user_id = $1 AND tenant_id = $2 AND name = $3 AND id != $4 AND deleted_at IS NULL`,
        [userId, tenantId, (data as any).name, id]
      );

      if (existingItem.rows.length > 0) {
        throw new Error(`${this.entityName} with this name already exists`);
      }
    }

    const setParts: string[] = [];
    const values: any[] = [];
    let paramIndex = 1;

    // Build dynamic update query
    Object.entries(data as Record<string, any>).forEach(([key, value]) => {
      if (value !== undefined) {
        setParts.push(`${key} = $${paramIndex++}`);
        values.push(value);
      }
    });

    if (setParts.length === 0) {
      throw new Error('No fields to update');
    }

    setParts.push(`updated_at = CURRENT_TIMESTAMP`);
    values.push(id, userId, tenantId);

    const sql = `UPDATE ${this.tableName} SET ${setParts.join(', ')} 
                 WHERE id = $${paramIndex} AND user_id = $${paramIndex + 1} AND tenant_id = $${paramIndex + 2} AND deleted_at IS NULL 
                 RETURNING *`;

    const result = await query(sql, values);

    if (result.rows.length === 0) {
      throw new Error(`${this.entityName} not found`);
    }

    return result.rows[0];
  }

  async delete(id: string, userId: string, tenantId: string): Promise<void> {
    const sql = `UPDATE ${this.tableName} 
                 SET deleted_at = CURRENT_TIMESTAMP 
                 WHERE id = $1 AND user_id = $2 AND tenant_id = $3 AND deleted_at IS NULL`;

    const result = await query(sql, [id, userId, tenantId]);

    if (result.rowCount === 0) {
      throw new Error(`${this.entityName} not found`);
    }
  }

  async search(
    userId: string,
    tenantId: string,
    searchTerm: string,
    page: number = 1,
    limit: number = 50
  ): Promise<PaginatedResponse<T>> {
    const offset = (page - 1) * limit;

    // Get total count
    const countSql = `SELECT COUNT(*) FROM ${this.tableName} 
                      WHERE user_id = $1 AND tenant_id = $2 AND deleted_at IS NULL 
                      AND name ILIKE $3`;
    const countResult = await query(countSql, [userId, tenantId, `%${searchTerm}%`]);
    const total = parseInt(countResult.rows[0].count);

    // Get paginated results
    const dataSql = `SELECT * FROM ${this.tableName} 
                     WHERE user_id = $1 AND tenant_id = $2 AND deleted_at IS NULL 
                     AND name ILIKE $3
                     ORDER BY created_at DESC 
                     LIMIT $4 OFFSET $5`;
    const result = await query(dataSql, [userId, tenantId, `%${searchTerm}%`, limit, offset]);

    return {
      data: result.rows,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }
}
