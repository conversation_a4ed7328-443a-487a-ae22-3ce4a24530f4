import { query } from '../database/connection';
import { Service, CreateServiceRequest, UpdateServiceRequest, PaginatedResponse } from '../types/models';

export class ServiceService {
  async getAll(
    userId: string,
    tenantId: string,
    page: number = 1,
    limit: number = 50,
    status?: string,
    serviceType?: string
  ): Promise<PaginatedResponse<Service>> {
    const offset = (page - 1) * limit;
    
    let whereConditions = 'user_id = $1 AND tenant_id = $2 AND deleted_at IS NULL';
    const params: any[] = [userId, tenantId];
    let paramIndex = 3;

    if (status) {
      whereConditions += ` AND status = $${paramIndex}`;
      params.push(status);
      paramIndex++;
    }

    if (serviceType) {
      whereConditions += ` AND service_type = $${paramIndex}`;
      params.push(serviceType);
      paramIndex++;
    }

    // Get total count
    const countSql = `SELECT COUNT(*) FROM services WHERE ${whereConditions}`;
    const countResult = await query(countSql, params);
    const total = parseInt(countResult.rows[0].count);

    // Get paginated results with customer info
    const dataSql = `
      SELECT s.*, c.name as customer_name, c.phone as customer_phone,
             u.name as assigned_to_name
      FROM services s
      LEFT JOIN customers c ON s.customer_id = c.id
      LEFT JOIN users u ON s.assigned_to = u.id
      WHERE ${whereConditions}
      ORDER BY s.created_at DESC 
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `;
    
    params.push(limit, offset);
    const result = await query(dataSql, params);

    return {
      data: result.rows,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  async getById(id: string, userId: string, tenantId: string): Promise<Service | null> {
    const sql = `
      SELECT s.*, c.name as customer_name, c.phone as customer_phone,
             u.name as assigned_to_name
      FROM services s
      LEFT JOIN customers c ON s.customer_id = c.id
      LEFT JOIN users u ON s.assigned_to = u.id
      WHERE s.id = $1 AND s.user_id = $2 AND s.tenant_id = $3 AND s.deleted_at IS NULL
    `;
    const result = await query(sql, [id, userId, tenantId]);
    return result.rows.length > 0 ? result.rows[0] : null;
  }

  async create(
    data: CreateServiceRequest,
    userId: string,
    tenantId: string
  ): Promise<Service> {
    // Verify customer exists and belongs to user
    const customerCheck = await query(
      'SELECT id FROM customers WHERE id = $1 AND tenant_id = $2 AND deleted_at IS NULL',
      [data.customer_id, tenantId]
    );
    
    if (customerCheck.rows.length === 0) {
      throw new Error('Customer not found');
    }

    // Verify assigned user exists if provided
    if (data.assigned_to) {
      const userCheck = await query(
        'SELECT id FROM users WHERE id = $1 AND tenant_id = $2 AND is_active = true',
        [data.assigned_to, tenantId]
      );
      
      if (userCheck.rows.length === 0) {
        throw new Error('Assigned user not found');
      }
    }

    const sql = `
      INSERT INTO services (
        user_id, customer_id, service_type, title, description, 
        priority, assigned_to, estimated_completion_date, total_cost, tenant_id
      )
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
      RETURNING *
    `;

    const values = [
      userId,
      data.customer_id,
      data.service_type || 'normal',
      data.title,
      data.description || null,
      data.priority || 'medium',
      data.assigned_to || null,
      data.estimated_completion_date || null,
      data.total_cost || 0,
      tenantId
    ];

    const result = await query(sql, values);
    return result.rows[0];
  }

  async update(
    id: string,
    data: UpdateServiceRequest,
    userId: string,
    tenantId: string
  ): Promise<Service> {
    const existingService = await this.getById(id, userId, tenantId);
    if (!existingService) {
      throw new Error('Service not found');
    }

    // Verify customer exists if being updated
    if (data.customer_id) {
      const customerCheck = await query(
        'SELECT id FROM customers WHERE id = $1 AND tenant_id = $2 AND deleted_at IS NULL',
        [data.customer_id, tenantId]
      );
      
      if (customerCheck.rows.length === 0) {
        throw new Error('Customer not found');
      }
    }

    // Verify assigned user exists if being updated
    if (data.assigned_to) {
      const userCheck = await query(
        'SELECT id FROM users WHERE id = $1 AND tenant_id = $2 AND is_active = true',
        [data.assigned_to, tenantId]
      );
      
      if (userCheck.rows.length === 0) {
        throw new Error('Assigned user not found');
      }
    }

    const setParts: string[] = [];
    const values: any[] = [];
    let paramIndex = 1;

    // Build dynamic update query
    Object.entries(data).forEach(([key, value]) => {
      if (value !== undefined) {
        setParts.push(`${key} = $${paramIndex++}`);
        values.push(value);
      }
    });

    if (setParts.length === 0) {
      throw new Error('No fields to update');
    }

    // Auto-set completed_at when status changes to completed
    if (data.status === 'completed' && existingService.status !== 'completed') {
      setParts.push(`completed_at = CURRENT_TIMESTAMP`);
    }

    setParts.push(`updated_at = CURRENT_TIMESTAMP`);
    values.push(id, userId, tenantId);

    const sql = `UPDATE services SET ${setParts.join(', ')} 
                 WHERE id = $${paramIndex} AND user_id = $${paramIndex + 1} AND tenant_id = $${paramIndex + 2} AND deleted_at IS NULL 
                 RETURNING *`;

    const result = await query(sql, values);

    if (result.rows.length === 0) {
      throw new Error('Service not found');
    }

    return result.rows[0];
  }

  async delete(id: string, userId: string, tenantId: string): Promise<void> {
    const sql = `UPDATE services 
                 SET deleted_at = CURRENT_TIMESTAMP 
                 WHERE id = $1 AND user_id = $2 AND tenant_id = $3 AND deleted_at IS NULL`;

    const result = await query(sql, [id, userId, tenantId]);

    if (result.rowCount === 0) {
      throw new Error('Service not found');
    }
  }

  async search(
    userId: string,
    tenantId: string,
    searchTerm: string,
    page: number = 1,
    limit: number = 50
  ): Promise<PaginatedResponse<Service>> {
    const offset = (page - 1) * limit;

    // Get total count
    const countSql = `
      SELECT COUNT(*) FROM services s
      LEFT JOIN customers c ON s.customer_id = c.id
      WHERE s.user_id = $1 AND s.tenant_id = $2 AND s.deleted_at IS NULL 
      AND (s.title ILIKE $3 OR s.description ILIKE $3 OR c.name ILIKE $3)
    `;
    const countResult = await query(countSql, [userId, tenantId, `%${searchTerm}%`]);
    const total = parseInt(countResult.rows[0].count);

    // Get paginated results
    const dataSql = `
      SELECT s.*, c.name as customer_name, c.phone as customer_phone,
             u.name as assigned_to_name
      FROM services s
      LEFT JOIN customers c ON s.customer_id = c.id
      LEFT JOIN users u ON s.assigned_to = u.id
      WHERE s.user_id = $1 AND s.tenant_id = $2 AND s.deleted_at IS NULL 
      AND (s.title ILIKE $3 OR s.description ILIKE $3 OR c.name ILIKE $3)
      ORDER BY s.created_at DESC 
      LIMIT $4 OFFSET $5
    `;
    const result = await query(dataSql, [userId, tenantId, `%${searchTerm}%`, limit, offset]);

    return {
      data: result.rows,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  async getServicesByStatus(userId: string, tenantId: string, status: string): Promise<Service[]> {
    const sql = `
      SELECT s.*, c.name as customer_name, c.phone as customer_phone,
             u.name as assigned_to_name
      FROM services s
      LEFT JOIN customers c ON s.customer_id = c.id
      LEFT JOIN users u ON s.assigned_to = u.id
      WHERE s.user_id = $1 AND s.tenant_id = $2 AND s.status = $3 AND s.deleted_at IS NULL
      ORDER BY s.created_at DESC
    `;
    const result = await query(sql, [userId, tenantId, status]);
    return result.rows;
  }

  async getRecentServices(userId: string, tenantId: string, limit: number = 10): Promise<Service[]> {
    const sql = `
      SELECT s.*, c.name as customer_name, c.phone as customer_phone,
             u.name as assigned_to_name
      FROM services s
      LEFT JOIN customers c ON s.customer_id = c.id
      LEFT JOIN users u ON s.assigned_to = u.id
      WHERE s.user_id = $1 AND s.tenant_id = $2 AND s.deleted_at IS NULL
      ORDER BY s.created_at DESC
      LIMIT $3
    `;
    const result = await query(sql, [userId, tenantId, limit]);
    return result.rows;
  }
}
