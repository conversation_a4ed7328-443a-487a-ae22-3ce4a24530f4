import { query } from '../database/connection';
import { CompanyInfo, CreateCompanyInfoRequest, UpdateCompanyInfoRequest } from '../types/models';
import { TenantAwareQuery } from '../middleware/tenantIsolation';

export class CompanyInfoService {
  async getCompanyInfo(userId: string, tenantId: string): Promise<CompanyInfo | null> {
    const tenantQuery = new TenantAwareQuery(tenantId);
    const { query: sql, params } = tenantQuery.addTenantFilter(
      'SELECT * FROM company_infos WHERE user_id = $1 AND deleted_at IS NULL',
      [userId]
    );

    const result = await query(sql, params);
    return result.rows.length > 0 ? result.rows[0] : null;
  }

  async createCompanyInfo(
    companyData: CreateCompanyInfoRequest,
    userId: string,
    tenantId: string
  ): Promise<CompanyInfo> {
    // Check if company info already exists for this user
    const existingInfo = await this.getCompanyInfo(userId, tenantId);
    if (existingInfo) {
      throw new Error('Company info already exists for this user. Use update instead.');
    }

    const {
      company_name,
      phone1,
      phone2,
      city,
      district,
      address,
      email,
      website,
      tax_number,
      tax_office,
    } = companyData;

    const result = await query(`
      INSERT INTO company_infos (
        user_id, company_name, phone1, phone2, city, district, 
        address, email, website, tax_number, tax_office, tenant_id
      )
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
      RETURNING *
    `, [
      userId,
      company_name,
      phone1,
      phone2,
      city,
      district,
      address,
      email,
      website,
      tax_number,
      tax_office,
      tenantId,
    ]);

    return result.rows[0];
  }

  async updateCompanyInfo(
    companyData: UpdateCompanyInfoRequest,
    userId: string,
    tenantId: string
  ): Promise<CompanyInfo> {
    const existingInfo = await this.getCompanyInfo(userId, tenantId);
    if (!existingInfo) {
      throw new Error('Company info not found for this user');
    }

    const setParts: string[] = [];
    const values: any[] = [];
    let paramIndex = 1;

    // Build dynamic update query
    Object.entries(companyData).forEach(([key, value]) => {
      if (value !== undefined) {
        setParts.push(`${key} = $${paramIndex++}`);
        values.push(value);
      }
    });

    if (setParts.length === 0) {
      throw new Error('No fields to update');
    }

    setParts.push(`updated_at = CURRENT_TIMESTAMP`);
    values.push(userId, tenantId);

    const tenantQuery = new TenantAwareQuery(tenantId);
    const { query: sql, params } = tenantQuery.addTenantFilter(
      `UPDATE company_infos SET ${setParts.join(', ')} WHERE user_id = $${paramIndex} RETURNING *`,
      values
    );

    const result = await query(sql, params);

    if (result.rows.length === 0) {
      throw new Error('Company info not found');
    }

    return result.rows[0];
  }

  async deleteCompanyInfo(userId: string, tenantId: string): Promise<void> {
    const tenantQuery = new TenantAwareQuery(tenantId);
    const { query: sql, params } = tenantQuery.addTenantFilter(
      'UPDATE company_infos SET deleted_at = CURRENT_TIMESTAMP WHERE user_id = $1 AND deleted_at IS NULL',
      [userId]
    );

    const result = await query(sql, params);

    if (result.rowCount === 0) {
      throw new Error('Company info not found');
    }
  }
}
