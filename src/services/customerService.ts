import { query } from '../database/connection';
import { Customer, PaginatedResponse } from '../types/models';
import { TenantAwareQuery } from '../middleware/tenantIsolation';

export interface CreateCustomerRequest {
  name: string;
  phone: string;
  email?: string;
  address?: string;
}

export class CustomerService {
  async createCustomer(customerData: CreateCustomerRequest, tenantId: string, createdBy: string): Promise<Customer> {
    const { name, phone, email, address } = customerData;

    const result = await query(`
      INSERT INTO customers (name, phone, email, address, tenant_id, created_by)
      VALUES ($1, $2, $3, $4, $5, $6)
      RETURNING *
    `, [name, phone, email, address, tenantId, createdBy]);

    return result.rows[0];
  }

  async getCustomerById(id: string, tenantId: string): Promise<Customer | null> {
    const tenantQuery = new TenantAwareQuery(tenantId);
    const { query: sql, params } = tenantQuery.addTenantFilter(
      'SELECT * FROM customers WHERE id = $1',
      [id]
    );

    const result = await query(sql, params);
    return result.rows.length > 0 ? result.rows[0] : null;
  }

  async getAllCustomers(
    tenantId: string,
    page: number = 1,
    limit: number = 10,
    search?: string
  ): Promise<PaginatedResponse<Customer>> {
    const offset = (page - 1) * limit;
    const tenantQuery = new TenantAwareQuery(tenantId);

    let baseQuery = 'SELECT COUNT(*) FROM customers';
    let searchCondition = '';
    let searchParams: any[] = [];

    if (search) {
      searchCondition = 'WHERE (name ILIKE $1 OR phone ILIKE $1 OR email ILIKE $1)';
      searchParams = [`%${search}%`];
    }

    // Get total count
    const { query: countSql, params: countParams } = tenantQuery.addTenantFilter(
      baseQuery + ' ' + searchCondition,
      searchParams
    );
    const countResult = await query(countSql, countParams);
    const total = parseInt(countResult.rows[0].count);

    // Get paginated results
    baseQuery = `
      SELECT c.*, u.name as created_by_name
      FROM customers c
      LEFT JOIN users u ON c.created_by = u.id
      ${searchCondition}
      ORDER BY c.created_at DESC
      LIMIT $${searchParams.length + 2} OFFSET $${searchParams.length + 3}
    `;

    const { query: dataSql, params: dataParams } = tenantQuery.addTenantFilter(
      baseQuery,
      [...searchParams, limit, offset]
    );

    const result = await query(dataSql, dataParams);

    return {
      data: result.rows,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  async updateCustomer(id: string, updateData: Partial<CreateCustomerRequest>, tenantId: string): Promise<Customer> {
    const { name, phone, email, address } = updateData;

    const setParts: string[] = [];
    const values: any[] = [];
    let paramIndex = 1;

    if (name !== undefined) {
      setParts.push(`name = $${paramIndex++}`);
      values.push(name);
    }
    if (phone !== undefined) {
      setParts.push(`phone = $${paramIndex++}`);
      values.push(phone);
    }
    if (email !== undefined) {
      setParts.push(`email = $${paramIndex++}`);
      values.push(email);
    }
    if (address !== undefined) {
      setParts.push(`address = $${paramIndex++}`);
      values.push(address);
    }

    setParts.push(`updated_at = CURRENT_TIMESTAMP`);

    const tenantQuery = new TenantAwareQuery(tenantId);
    const { query: sql, params } = tenantQuery.addTenantFilter(
      `UPDATE customers SET ${setParts.join(', ')} WHERE id = $${paramIndex} RETURNING *`,
      [...values, id]
    );

    const result = await query(sql, params);

    if (result.rows.length === 0) {
      throw new Error('Customer not found');
    }

    return result.rows[0];
  }

  async deleteCustomer(id: string, tenantId: string): Promise<void> {
    const tenantQuery = new TenantAwareQuery(tenantId);
    const { query: sql, params } = tenantQuery.addTenantFilter(
      'DELETE FROM customers WHERE id = $1',
      [id]
    );

    const result = await query(sql, params);

    if (result.rowCount === 0) {
      throw new Error('Customer not found');
    }
  }

  async getCustomerDevices(customerId: string, tenantId: string): Promise<any[]> {
    const tenantQuery = new TenantAwareQuery(tenantId);
    const { query: sql, params } = tenantQuery.addTenantFilter(
      'SELECT * FROM devices WHERE customer_id = $1 ORDER BY created_at DESC',
      [customerId]
    );

    const result = await query(sql, params);
    return result.rows;
  }
}
