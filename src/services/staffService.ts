import { BaseCrudService } from './baseCrudService';
import { Staff, CreateStaffRequest, UpdateStaffRequest } from '../types/models';
import { query } from '../database/connection';

export class StaffService extends BaseCrudService<Staff, CreateStaffRequest, UpdateStaffRequest> {
  constructor() {
    super('staff', 'Staff member');
  }

  // Override create to check for duplicate email
  async create(
    data: CreateStaffRequest,
    userId: string,
    tenantId: string
  ): Promise<Staff> {
    // Check for duplicate email if provided
    if (data.email) {
      const existingStaff = await query(
        `SELECT id FROM ${this.tableName} WHERE user_id = $1 AND tenant_id = $2 AND email = $3 AND deleted_at IS NULL`,
        [userId, tenantId, data.email]
      );

      if (existingStaff.rows.length > 0) {
        throw new Error('Staff member with this email already exists');
      }
    }

    return super.create(data, userId, tenantId);
  }

  // Override update to check for duplicate email
  async update(
    id: string,
    data: UpdateStaffRequest,
    userId: string,
    tenantId: string
  ): Promise<Staff> {
    if (data.email) {
      // Check for duplicate email
      const existingStaff = await query(
        `SELECT id FROM ${this.tableName} WHERE user_id = $1 AND tenant_id = $2 AND email = $3 AND id != $4 AND deleted_at IS NULL`,
        [userId, tenantId, data.email, id]
      );

      if (existingStaff.rows.length > 0) {
        throw new Error('Staff member with this email already exists');
      }
    }

    return super.update(id, data, userId, tenantId);
  }

  async getActiveStaff(userId: string, tenantId: string): Promise<Staff[]> {
    const sql = `SELECT * FROM ${this.tableName} 
                 WHERE user_id = $1 AND tenant_id = $2 AND is_active = true AND deleted_at IS NULL 
                 ORDER BY name ASC`;
    const result = await query(sql, [userId, tenantId]);
    return result.rows;
  }

  async getStaffByPosition(userId: string, tenantId: string, position: string): Promise<Staff[]> {
    const sql = `SELECT * FROM ${this.tableName} 
                 WHERE user_id = $1 AND tenant_id = $2 AND position ILIKE $3 AND deleted_at IS NULL 
                 ORDER BY name ASC`;
    const result = await query(sql, [userId, tenantId, `%${position}%`]);
    return result.rows;
  }

  async getStaffStats(userId: string, tenantId: string) {
    const sql = `
      SELECT 
        COUNT(*) as total,
        COUNT(CASE WHEN is_active = true THEN 1 END) as active,
        COUNT(CASE WHEN is_active = false THEN 1 END) as inactive,
        AVG(salary) as average_salary
      FROM ${this.tableName} 
      WHERE user_id = $1 AND tenant_id = $2 AND deleted_at IS NULL
    `;
    const result = await query(sql, [userId, tenantId]);
    return result.rows[0];
  }

  // Override search to search by name, position, and email
  async search(
    userId: string,
    tenantId: string,
    searchTerm: string,
    page: number = 1,
    limit: number = 50
  ) {
    const offset = (page - 1) * limit;

    // Get total count
    const countSql = `SELECT COUNT(*) FROM ${this.tableName} 
                      WHERE user_id = $1 AND tenant_id = $2 AND deleted_at IS NULL 
                      AND (name ILIKE $3 OR position ILIKE $3 OR email ILIKE $3)`;
    const countResult = await query(countSql, [userId, tenantId, `%${searchTerm}%`]);
    const total = parseInt(countResult.rows[0].count);

    // Get paginated results
    const dataSql = `SELECT * FROM ${this.tableName} 
                     WHERE user_id = $1 AND tenant_id = $2 AND deleted_at IS NULL 
                     AND (name ILIKE $3 OR position ILIKE $3 OR email ILIKE $3)
                     ORDER BY name ASC 
                     LIMIT $4 OFFSET $5`;
    const result = await query(dataSql, [userId, tenantId, `%${searchTerm}%`, limit, offset]);

    return {
      data: result.rows,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }
}
