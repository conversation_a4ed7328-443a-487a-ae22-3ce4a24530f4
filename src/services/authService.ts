import bcrypt from 'bcryptjs';
import { query } from '../database/connection';
import { generateToken } from '../utils/jwt';
import { User, Tenant, LoginRequest, AuthResponse } from '../types/models';

export class AuthService {
  async login(loginData: LoginRequest): Promise<AuthResponse> {
    const { email, password } = loginData;

    // Find user with tenant information
    const userResult = await query(`
      SELECT u.*, t.name as tenant_name, t.slug as tenant_slug, t.logo as tenant_logo, t.is_active as tenant_active
      FROM users u
      JOIN tenants t ON u.tenant_id = t.id
      WHERE u.email = $1 AND u.is_active = true AND t.is_active = true
    `, [email]);

    if (userResult.rows.length === 0) {
      throw new Error('Invalid credentials');
    }

    const user = userResult.rows[0];

    // Verify password
    const isPasswordValid = await bcrypt.compare(password, user.password);
    if (!isPasswordValid) {
      throw new Error('Invalid credentials');
    }

    // Create user and tenant objects
    const userObj: User = {
      id: user.id,
      name: user.name,
      email: user.email,
      password: user.password,
      role: user.role,
      tenant_id: user.tenant_id,
      is_active: user.is_active,
      created_at: user.created_at,
      updated_at: user.updated_at,
    };

    const tenantObj: Tenant = {
      id: user.tenant_id,
      name: user.tenant_name,
      slug: user.tenant_slug,
      logo: user.tenant_logo,
      is_active: user.tenant_active,
      created_at: user.created_at,
      updated_at: user.updated_at,
    };

    // Generate JWT token
    const token = generateToken(userObj, tenantObj);

    // Remove password from response
    const { password: _, ...userWithoutPassword } = userObj;

    return {
      token,
      user: userWithoutPassword,
      tenant: tenantObj,
    };
  }

  async register(userData: any, tenantId: string): Promise<User> {
    const { name, email, password, role } = userData;

    // Check if user already exists
    const existingUser = await query('SELECT id FROM users WHERE email = $1', [email]);
    if (existingUser.rows.length > 0) {
      throw new Error('User with this email already exists');
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 12);

    // Create user
    const result = await query(`
      INSERT INTO users (name, email, password, role, tenant_id)
      VALUES ($1, $2, $3, $4, $5)
      RETURNING *
    `, [name, email, hashedPassword, role, tenantId]);

    return result.rows[0];
  }

  async changePassword(userId: string, currentPassword: string, newPassword: string): Promise<void> {
    // Get current user
    const userResult = await query('SELECT password FROM users WHERE id = $1', [userId]);
    if (userResult.rows.length === 0) {
      throw new Error('User not found');
    }

    const user = userResult.rows[0];

    // Verify current password
    const isCurrentPasswordValid = await bcrypt.compare(currentPassword, user.password);
    if (!isCurrentPasswordValid) {
      throw new Error('Current password is incorrect');
    }

    // Hash new password
    const hashedNewPassword = await bcrypt.hash(newPassword, 12);

    // Update password
    await query('UPDATE users SET password = $1, updated_at = CURRENT_TIMESTAMP WHERE id = $2', [
      hashedNewPassword,
      userId,
    ]);
  }

  async getUserById(userId: string): Promise<User | null> {
    const result = await query('SELECT * FROM users WHERE id = $1', [userId]);
    return result.rows.length > 0 ? result.rows[0] : null;
  }
}
