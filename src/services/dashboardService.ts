import { query } from '../database/connection';
import { DashboardStats } from '../types/models';
import { ServiceService } from './serviceService';
import { CashTransactionService } from './cashTransactionService';

export class DashboardService {
  private serviceService: ServiceService;
  private cashTransactionService: CashTransactionService;

  constructor() {
    this.serviceService = new ServiceService();
    this.cashTransactionService = new CashTransactionService();
  }

  async getDashboardStats(userId: string, tenantId: string): Promise<DashboardStats> {
    const today = new Date().toISOString().split('T')[0];
    const weekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];
    const monthStart = new Date(new Date().getFullYear(), new Date().getMonth(), 1).toISOString().split('T')[0];

    // Get service statistics
    const serviceStats = await this.getServiceStats(userId, tenantId, today, weekAgo, monthStart);
    
    // Get customer statistics
    const customerStats = await this.getCustomerStats(userId, tenantId, today, weekAgo, monthStart);
    
    // Get staff statistics
    const staffStats = await this.getStaffStats(userId, tenantId);
    
    // Get cash statistics
    const cashStats = await this.getCashStats(userId, tenantId, today, monthStart);
    
    // Get recent services
    const recentServices = await this.serviceService.getRecentServices(userId, tenantId, 5);
    
    // Get recent transactions
    const recentTransactions = await this.cashTransactionService.getRecentTransactions(userId, tenantId, 5);

    return {
      services: serviceStats,
      customers: customerStats,
      staff: staffStats,
      cash: cashStats,
      recent_services: recentServices,
      recent_transactions: recentTransactions,
    };
  }

  private async getServiceStats(userId: string, tenantId: string, today: string, weekAgo: string, monthStart: string) {
    const sql = `
      SELECT 
        COUNT(*) as total,
        COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending,
        COUNT(CASE WHEN status = 'in_progress' THEN 1 END) as in_progress,
        COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed,
        COUNT(CASE WHEN status = 'cancelled' THEN 1 END) as cancelled,
        COUNT(CASE WHEN DATE(created_at) = $3 THEN 1 END) as today,
        COUNT(CASE WHEN DATE(created_at) >= $4 THEN 1 END) as this_week,
        COUNT(CASE WHEN DATE(created_at) >= $5 THEN 1 END) as this_month
      FROM services 
      WHERE user_id = $1 AND tenant_id = $2 AND deleted_at IS NULL
    `;
    
    const result = await query(sql, [userId, tenantId, today, weekAgo, monthStart]);
    const row = result.rows[0];
    
    return {
      total: parseInt(row.total),
      pending: parseInt(row.pending),
      in_progress: parseInt(row.in_progress),
      completed: parseInt(row.completed),
      cancelled: parseInt(row.cancelled),
      today: parseInt(row.today),
      this_week: parseInt(row.this_week),
      this_month: parseInt(row.this_month),
    };
  }

  private async getCustomerStats(userId: string, tenantId: string, today: string, weekAgo: string, monthStart: string) {
    const sql = `
      SELECT
        COUNT(*) as total,
        COUNT(CASE WHEN DATE(created_at) = $1 THEN 1 END) as new_today,
        COUNT(CASE WHEN DATE(created_at) >= $2 THEN 1 END) as new_this_week,
        COUNT(CASE WHEN DATE(created_at) >= $3 THEN 1 END) as new_this_month
      FROM customers
      WHERE tenant_id = $4 AND deleted_at IS NULL
    `;

    const result = await query(sql, [today, weekAgo, monthStart, tenantId]);
    const row = result.rows[0];

    return {
      total: parseInt(row.total),
      new_today: parseInt(row.new_today),
      new_this_week: parseInt(row.new_this_week),
      new_this_month: parseInt(row.new_this_month),
    };
  }

  private async getStaffStats(userId: string, tenantId: string) {
    const sql = `
      SELECT 
        COUNT(*) as total,
        COUNT(CASE WHEN is_active = true THEN 1 END) as active,
        COUNT(CASE WHEN is_active = false THEN 1 END) as inactive
      FROM staff 
      WHERE user_id = $1 AND tenant_id = $2 AND deleted_at IS NULL
    `;
    
    const result = await query(sql, [userId, tenantId]);
    const row = result.rows[0];
    
    return {
      total: parseInt(row.total),
      active: parseInt(row.active),
      inactive: parseInt(row.inactive),
    };
  }

  private async getCashStats(userId: string, tenantId: string, today: string, monthStart: string) {
    // Get all time totals
    const allTimeSql = `
      SELECT 
        SUM(CASE WHEN transaction_type = 'income' THEN amount ELSE 0 END) as total_income,
        SUM(CASE WHEN transaction_type = 'expense' THEN amount ELSE 0 END) as total_expense
      FROM cash_transactions 
      WHERE user_id = $1 AND tenant_id = $2 AND deleted_at IS NULL
    `;
    
    const allTimeResult = await query(allTimeSql, [userId, tenantId]);
    const allTime = allTimeResult.rows[0];
    
    // Get today's totals
    const todaySql = `
      SELECT 
        SUM(CASE WHEN transaction_type = 'income' THEN amount ELSE 0 END) as today_income,
        SUM(CASE WHEN transaction_type = 'expense' THEN amount ELSE 0 END) as today_expense
      FROM cash_transactions 
      WHERE user_id = $1 AND tenant_id = $2 AND deleted_at IS NULL AND transaction_date = $3
    `;
    
    const todayResult = await query(todaySql, [userId, tenantId, today]);
    const todayData = todayResult.rows[0];
    
    // Get this month's totals
    const monthSql = `
      SELECT 
        SUM(CASE WHEN transaction_type = 'income' THEN amount ELSE 0 END) as this_month_income,
        SUM(CASE WHEN transaction_type = 'expense' THEN amount ELSE 0 END) as this_month_expense
      FROM cash_transactions 
      WHERE user_id = $1 AND tenant_id = $2 AND deleted_at IS NULL AND transaction_date >= $3
    `;
    
    const monthResult = await query(monthSql, [userId, tenantId, monthStart]);
    const monthData = monthResult.rows[0];
    
    const totalIncome = parseFloat(allTime.total_income) || 0;
    const totalExpense = parseFloat(allTime.total_expense) || 0;
    
    return {
      total_income: totalIncome,
      total_expense: totalExpense,
      net_balance: totalIncome - totalExpense,
      today_income: parseFloat(todayData.today_income) || 0,
      today_expense: parseFloat(todayData.today_expense) || 0,
      this_month_income: parseFloat(monthData.this_month_income) || 0,
      this_month_expense: parseFloat(monthData.this_month_expense) || 0,
    };
  }

  async getServiceTrends(userId: string, tenantId: string, days: number = 30) {
    const startDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000).toISOString().split('T')[0];
    
    const sql = `
      SELECT 
        DATE(created_at) as date,
        COUNT(*) as total_services,
        COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_services
      FROM services 
      WHERE user_id = $1 AND tenant_id = $2 AND deleted_at IS NULL 
      AND DATE(created_at) >= $3
      GROUP BY DATE(created_at)
      ORDER BY date ASC
    `;
    
    const result = await query(sql, [userId, tenantId, startDate]);
    return result.rows;
  }

  async getCashFlowTrends(userId: string, tenantId: string, days: number = 30) {
    const startDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000).toISOString().split('T')[0];
    
    const sql = `
      SELECT 
        transaction_date as date,
        SUM(CASE WHEN transaction_type = 'income' THEN amount ELSE 0 END) as income,
        SUM(CASE WHEN transaction_type = 'expense' THEN amount ELSE 0 END) as expense
      FROM cash_transactions 
      WHERE user_id = $1 AND tenant_id = $2 AND deleted_at IS NULL 
      AND transaction_date >= $3
      GROUP BY transaction_date
      ORDER BY transaction_date ASC
    `;
    
    const result = await query(sql, [userId, tenantId, startDate]);
    return result.rows;
  }

  async getTopCustomers(userId: string, tenantId: string, limit: number = 10) {
    const sql = `
      SELECT 
        c.id,
        c.name,
        c.phone,
        COUNT(s.id) as service_count,
        SUM(s.total_cost) as total_spent
      FROM customers c
      LEFT JOIN services s ON c.id = s.customer_id AND s.deleted_at IS NULL
      WHERE c.tenant_id = $2 AND c.deleted_at IS NULL
      GROUP BY c.id, c.name, c.phone
      HAVING COUNT(s.id) > 0
      ORDER BY service_count DESC, total_spent DESC
      LIMIT $3
    `;
    
    const result = await query(sql, [userId, tenantId, limit]);
    return result.rows;
  }
}
