import { query } from '../database/connection';
import { CashTransaction, CreateCashTransactionRequest, UpdateCashTransactionRequest, PaginatedResponse } from '../types/models';

export class CashTransactionService {
  async getAll(
    userId: string,
    tenantId: string,
    page: number = 1,
    limit: number = 50,
    transactionType?: string,
    category?: string,
    startDate?: string,
    endDate?: string
  ): Promise<PaginatedResponse<CashTransaction>> {
    const offset = (page - 1) * limit;
    
    let whereConditions = 'user_id = $1 AND tenant_id = $2 AND deleted_at IS NULL';
    const params: any[] = [userId, tenantId];
    let paramIndex = 3;

    if (transactionType) {
      whereConditions += ` AND transaction_type = $${paramIndex}`;
      params.push(transactionType);
      paramIndex++;
    }

    if (category) {
      whereConditions += ` AND category = $${paramIndex}`;
      params.push(category);
      paramIndex++;
    }

    if (startDate) {
      whereConditions += ` AND transaction_date >= $${paramIndex}`;
      params.push(startDate);
      paramIndex++;
    }

    if (endDate) {
      whereConditions += ` AND transaction_date <= $${paramIndex}`;
      params.push(endDate);
      paramIndex++;
    }

    // Get total count
    const countSql = `SELECT COUNT(*) FROM cash_transactions WHERE ${whereConditions}`;
    const countResult = await query(countSql, params);
    const total = parseInt(countResult.rows[0].count);

    // Get paginated results
    const dataSql = `SELECT * FROM cash_transactions 
                     WHERE ${whereConditions}
                     ORDER BY transaction_date DESC, created_at DESC 
                     LIMIT $${paramIndex} OFFSET $${paramIndex + 1}`;
    
    params.push(limit, offset);
    const result = await query(dataSql, params);

    return {
      data: result.rows,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  async getById(id: string, userId: string, tenantId: string): Promise<CashTransaction | null> {
    const sql = 'SELECT * FROM cash_transactions WHERE id = $1 AND user_id = $2 AND tenant_id = $3 AND deleted_at IS NULL';
    const result = await query(sql, [id, userId, tenantId]);
    return result.rows.length > 0 ? result.rows[0] : null;
  }

  async create(
    data: CreateCashTransactionRequest,
    userId: string,
    tenantId: string
  ): Promise<CashTransaction> {
    const sql = `
      INSERT INTO cash_transactions (
        user_id, amount, transaction_type, category, description, 
        reference_id, reference_type, transaction_date, tenant_id
      )
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
      RETURNING *
    `;

    const values = [
      userId,
      data.amount,
      data.transaction_type,
      data.category || null,
      data.description,
      data.reference_id || null,
      data.reference_type || null,
      data.transaction_date || new Date().toISOString().split('T')[0],
      tenantId
    ];

    const result = await query(sql, values);
    return result.rows[0];
  }

  async update(
    id: string,
    data: UpdateCashTransactionRequest,
    userId: string,
    tenantId: string
  ): Promise<CashTransaction> {
    const existingTransaction = await this.getById(id, userId, tenantId);
    if (!existingTransaction) {
      throw new Error('Transaction not found');
    }

    const setParts: string[] = [];
    const values: any[] = [];
    let paramIndex = 1;

    // Build dynamic update query
    Object.entries(data).forEach(([key, value]) => {
      if (value !== undefined) {
        setParts.push(`${key} = $${paramIndex++}`);
        values.push(value);
      }
    });

    if (setParts.length === 0) {
      throw new Error('No fields to update');
    }

    setParts.push(`updated_at = CURRENT_TIMESTAMP`);
    values.push(id, userId, tenantId);

    const sql = `UPDATE cash_transactions SET ${setParts.join(', ')} 
                 WHERE id = $${paramIndex} AND user_id = $${paramIndex + 1} AND tenant_id = $${paramIndex + 2} AND deleted_at IS NULL 
                 RETURNING *`;

    const result = await query(sql, values);

    if (result.rows.length === 0) {
      throw new Error('Transaction not found');
    }

    return result.rows[0];
  }

  async delete(id: string, userId: string, tenantId: string): Promise<void> {
    const sql = `UPDATE cash_transactions 
                 SET deleted_at = CURRENT_TIMESTAMP 
                 WHERE id = $1 AND user_id = $2 AND tenant_id = $3 AND deleted_at IS NULL`;

    const result = await query(sql, [id, userId, tenantId]);

    if (result.rowCount === 0) {
      throw new Error('Transaction not found');
    }
  }

  async search(
    userId: string,
    tenantId: string,
    searchTerm: string,
    page: number = 1,
    limit: number = 50
  ): Promise<PaginatedResponse<CashTransaction>> {
    const offset = (page - 1) * limit;

    // Get total count
    const countSql = `SELECT COUNT(*) FROM cash_transactions 
                      WHERE user_id = $1 AND tenant_id = $2 AND deleted_at IS NULL 
                      AND (description ILIKE $3 OR category ILIKE $3)`;
    const countResult = await query(countSql, [userId, tenantId, `%${searchTerm}%`]);
    const total = parseInt(countResult.rows[0].count);

    // Get paginated results
    const dataSql = `SELECT * FROM cash_transactions 
                     WHERE user_id = $1 AND tenant_id = $2 AND deleted_at IS NULL 
                     AND (description ILIKE $3 OR category ILIKE $3)
                     ORDER BY transaction_date DESC, created_at DESC 
                     LIMIT $4 OFFSET $5`;
    const result = await query(dataSql, [userId, tenantId, `%${searchTerm}%`, limit, offset]);

    return {
      data: result.rows,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  async getCashSummary(userId: string, tenantId: string, startDate?: string, endDate?: string) {
    let whereConditions = 'user_id = $1 AND tenant_id = $2 AND deleted_at IS NULL';
    const params: any[] = [userId, tenantId];
    let paramIndex = 3;

    if (startDate) {
      whereConditions += ` AND transaction_date >= $${paramIndex}`;
      params.push(startDate);
      paramIndex++;
    }

    if (endDate) {
      whereConditions += ` AND transaction_date <= $${paramIndex}`;
      params.push(endDate);
      paramIndex++;
    }

    const sql = `
      SELECT 
        SUM(CASE WHEN transaction_type = 'income' THEN amount ELSE 0 END) as total_income,
        SUM(CASE WHEN transaction_type = 'expense' THEN amount ELSE 0 END) as total_expense,
        SUM(CASE WHEN transaction_type = 'income' THEN amount ELSE -amount END) as net_balance,
        COUNT(*) as total_transactions
      FROM cash_transactions 
      WHERE ${whereConditions}
    `;
    
    const result = await query(sql, params);
    return result.rows[0];
  }

  async getRecentTransactions(userId: string, tenantId: string, limit: number = 10): Promise<CashTransaction[]> {
    const sql = `SELECT * FROM cash_transactions 
                 WHERE user_id = $1 AND tenant_id = $2 AND deleted_at IS NULL
                 ORDER BY transaction_date DESC, created_at DESC 
                 LIMIT $3`;
    const result = await query(sql, [userId, tenantId, limit]);
    return result.rows;
  }

  async getTransactionsByCategory(userId: string, tenantId: string): Promise<any[]> {
    const sql = `
      SELECT 
        category,
        transaction_type,
        SUM(amount) as total_amount,
        COUNT(*) as transaction_count
      FROM cash_transactions 
      WHERE user_id = $1 AND tenant_id = $2 AND deleted_at IS NULL AND category IS NOT NULL
      GROUP BY category, transaction_type
      ORDER BY total_amount DESC
    `;
    const result = await query(sql, [userId, tenantId]);
    return result.rows;
  }
}
