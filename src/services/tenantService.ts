import { query } from '../database/connection';
import { Tenant, CreateTenantRequest, PaginatedResponse } from '../types/models';

export class TenantService {
  async createTenant(tenantData: CreateTenantRequest): Promise<Tenant> {
    const { name, slug, logo } = tenantData;

    // Check if slug already exists
    const existingTenant = await query('SELECT id FROM tenants WHERE slug = $1', [slug]);
    if (existingTenant.rows.length > 0) {
      throw new Error('Tenant with this slug already exists');
    }

    const result = await query(`
      INSERT INTO tenants (name, slug, logo)
      VALUES ($1, $2, $3)
      RETURNING *
    `, [name, slug, logo]);

    return result.rows[0];
  }

  async getTenantById(id: string): Promise<Tenant | null> {
    const result = await query('SELECT * FROM tenants WHERE id = $1', [id]);
    return result.rows.length > 0 ? result.rows[0] : null;
  }

  async getTenantBySlug(slug: string): Promise<Tenant | null> {
    const result = await query('SELECT * FROM tenants WHERE slug = $1', [slug]);
    return result.rows.length > 0 ? result.rows[0] : null;
  }

  async getAllTenants(page: number = 1, limit: number = 10): Promise<PaginatedResponse<Tenant>> {
    const offset = (page - 1) * limit;

    // Get total count
    const countResult = await query('SELECT COUNT(*) FROM tenants');
    const total = parseInt(countResult.rows[0].count);

    // Get paginated results
    const result = await query(`
      SELECT * FROM tenants
      ORDER BY created_at DESC
      LIMIT $1 OFFSET $2
    `, [limit, offset]);

    return {
      data: result.rows,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  async updateTenant(id: string, updateData: Partial<CreateTenantRequest>): Promise<Tenant> {
    const { name, slug, logo } = updateData;
    
    // Check if new slug conflicts with existing tenant
    if (slug) {
      const existingTenant = await query('SELECT id FROM tenants WHERE slug = $1 AND id != $2', [slug, id]);
      if (existingTenant.rows.length > 0) {
        throw new Error('Tenant with this slug already exists');
      }
    }

    const setParts: string[] = [];
    const values: any[] = [];
    let paramIndex = 1;

    if (name !== undefined) {
      setParts.push(`name = $${paramIndex++}`);
      values.push(name);
    }
    if (slug !== undefined) {
      setParts.push(`slug = $${paramIndex++}`);
      values.push(slug);
    }
    if (logo !== undefined) {
      setParts.push(`logo = $${paramIndex++}`);
      values.push(logo);
    }

    setParts.push(`updated_at = CURRENT_TIMESTAMP`);
    values.push(id);

    const result = await query(`
      UPDATE tenants 
      SET ${setParts.join(', ')}
      WHERE id = $${paramIndex}
      RETURNING *
    `, values);

    if (result.rows.length === 0) {
      throw new Error('Tenant not found');
    }

    return result.rows[0];
  }

  async deleteTenant(id: string): Promise<void> {
    const result = await query('DELETE FROM tenants WHERE id = $1', [id]);
    
    if (result.rowCount === 0) {
      throw new Error('Tenant not found');
    }
  }

  async toggleTenantStatus(id: string): Promise<Tenant> {
    const result = await query(`
      UPDATE tenants 
      SET is_active = NOT is_active, updated_at = CURRENT_TIMESTAMP
      WHERE id = $1
      RETURNING *
    `, [id]);

    if (result.rows.length === 0) {
      throw new Error('Tenant not found');
    }

    return result.rows[0];
  }
}
