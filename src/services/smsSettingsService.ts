import { query } from '../database/connection';
import { SmsSettings, CreateSmsSettingsRequest, UpdateSmsSettingsRequest } from '../types/models';
import { TenantAwareQuery } from '../middleware/tenantIsolation';
import bcrypt from 'bcryptjs';

export class SmsSettingsService {
  async getSmsSettings(userId: string, tenantId: string): Promise<SmsSettings | null> {
    const tenantQuery = new TenantAwareQuery(tenantId);
    const { query: sql, params } = tenantQuery.addTenantFilter(
      'SELECT * FROM sms_settings WHERE user_id = $1 AND deleted_at IS NULL',
      [userId]
    );

    const result = await query(sql, params);
    return result.rows.length > 0 ? result.rows[0] : null;
  }

  async createSmsSettings(
    settingsData: CreateSmsSettingsRequest,
    userId: string,
    tenantId: string
  ): Promise<SmsSettings> {
    // Check if SMS settings already exist for this user
    const existingSettings = await this.getSmsSettings(userId, tenantId);
    if (existingSettings) {
      throw new Error('SMS settings already exist for this user. Use update instead.');
    }

    const {
      sender_company,
      message_header,
      username,
      password,
      sms_template,
    } = settingsData;

    // Validate SMS template contains required dynamic fields
    this.validateSmsTemplate(sms_template);

    // Hash the password for security
    const hashedPassword = await bcrypt.hash(password, 12);

    const result = await query(`
      INSERT INTO sms_settings (
        user_id, sender_company, message_header, username, password, sms_template, tenant_id
      )
      VALUES ($1, $2, $3, $4, $5, $6, $7)
      RETURNING *
    `, [
      userId,
      sender_company,
      message_header,
      username,
      hashedPassword,
      sms_template,
      tenantId,
    ]);

    return result.rows[0];
  }

  async updateSmsSettings(
    settingsData: UpdateSmsSettingsRequest,
    userId: string,
    tenantId: string
  ): Promise<SmsSettings> {
    const existingSettings = await this.getSmsSettings(userId, tenantId);
    if (!existingSettings) {
      throw new Error('SMS settings not found for this user');
    }

    const setParts: string[] = [];
    const values: any[] = [];
    let paramIndex = 1;

    // Build dynamic update query
    for (const [key, value] of Object.entries(settingsData)) {
      if (value !== undefined) {
        if (key === 'password' && typeof value === 'string') {
          // Hash password if it's being updated
          const hashedPassword = await bcrypt.hash(value, 12);
          setParts.push(`${key} = $${paramIndex++}`);
          values.push(hashedPassword);
        } else if (key === 'sms_template' && typeof value === 'string') {
          // Validate SMS template
          this.validateSmsTemplate(value);
          setParts.push(`${key} = $${paramIndex++}`);
          values.push(value);
        } else {
          setParts.push(`${key} = $${paramIndex++}`);
          values.push(value);
        }
      }
    }

    if (setParts.length === 0) {
      throw new Error('No fields to update');
    }

    setParts.push(`updated_at = CURRENT_TIMESTAMP`);
    values.push(userId, tenantId);

    const tenantQuery = new TenantAwareQuery(tenantId);
    const { query: sql, params } = tenantQuery.addTenantFilter(
      `UPDATE sms_settings SET ${setParts.join(', ')} WHERE user_id = $${paramIndex} RETURNING *`,
      values
    );

    const result = await query(sql, params);

    if (result.rows.length === 0) {
      throw new Error('SMS settings not found');
    }

    return result.rows[0];
  }

  async deleteSmsSettings(userId: string, tenantId: string): Promise<void> {
    const tenantQuery = new TenantAwareQuery(tenantId);
    const { query: sql, params } = tenantQuery.addTenantFilter(
      'UPDATE sms_settings SET deleted_at = CURRENT_TIMESTAMP WHERE user_id = $1 AND deleted_at IS NULL',
      [userId]
    );

    const result = await query(sql, params);

    if (result.rowCount === 0) {
      throw new Error('SMS settings not found');
    }
  }

  private validateSmsTemplate(template: string): void {
    const requiredFields = ['[[MUSTERIADI]]', '[[MARKAADI]]', '[[TARIH]]'];
    const missingFields = requiredFields.filter(field => !template.includes(field));
    
    if (missingFields.length > 0) {
      throw new Error(`SMS template must contain the following dynamic fields: ${missingFields.join(', ')}`);
    }
  }

  async processSmsTemplate(
    userId: string,
    tenantId: string,
    customerName: string,
    brandName: string,
    date: string
  ): Promise<string> {
    const settings = await this.getSmsSettings(userId, tenantId);
    if (!settings) {
      throw new Error('SMS settings not found');
    }

    let processedTemplate = settings.sms_template;
    processedTemplate = processedTemplate.replace(/\[\[MUSTERIADI\]\]/g, customerName);
    processedTemplate = processedTemplate.replace(/\[\[MARKAADI\]\]/g, brandName);
    processedTemplate = processedTemplate.replace(/\[\[TARIH\]\]/g, date);

    return processedTemplate;
  }

  // Get SMS settings without password for security
  async getSmsSettingsSecure(userId: string, tenantId: string): Promise<Omit<SmsSettings, 'password'> | null> {
    const settings = await this.getSmsSettings(userId, tenantId);
    if (!settings) {
      return null;
    }

    const { password, ...secureSettings } = settings;
    return secureSettings;
  }
}
