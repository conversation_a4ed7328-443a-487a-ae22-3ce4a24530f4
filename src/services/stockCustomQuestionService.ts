import { BaseCrudService } from './baseCrudService';
import { StockCustomQuestion, CreateStockCustomQuestionRequest, UpdateStockCustomQuestionRequest } from '../types/models';
import { query } from '../database/connection';

export class StockCustomQuestionService extends BaseCrudService<StockCustomQuestion, CreateStockCustomQuestionRequest, UpdateStockCustomQuestionRequest> {
  constructor() {
    super('stock_custom_questions', 'Stock custom question');
  }

  // Override create to check for duplicate question text
  async create(
    data: CreateStockCustomQuestionRequest,
    userId: string,
    tenantId: string
  ): Promise<StockCustomQuestion> {
    // Check for duplicate question text
    const existingQuestion = await query(
      `SELECT id FROM ${this.tableName} WHERE user_id = $1 AND tenant_id = $2 AND question_text = $3 AND deleted_at IS NULL`,
      [userId, tenantId, data.question_text]
    );

    if (existingQuestion.rows.length > 0) {
      throw new Error('Question with this text already exists');
    }

    return super.create(data, userId, tenantId);
  }

  // Override update to check for duplicate question text
  async update(
    id: string,
    data: UpdateStockCustomQuestionRequest,
    userId: string,
    tenantId: string
  ): Promise<StockCustomQuestion> {
    if (data.question_text) {
      // Check for duplicate question text
      const existingQuestion = await query(
        `SELECT id FROM ${this.tableName} WHERE user_id = $1 AND tenant_id = $2 AND question_text = $3 AND id != $4 AND deleted_at IS NULL`,
        [userId, tenantId, data.question_text, id]
      );

      if (existingQuestion.rows.length > 0) {
        throw new Error('Question with this text already exists');
      }
    }

    return super.update(id, data, userId, tenantId);
  }

  // Override search to search by question text instead of name
  async search(
    userId: string,
    tenantId: string,
    searchTerm: string,
    page: number = 1,
    limit: number = 50
  ) {
    const offset = (page - 1) * limit;

    // Get total count
    const countSql = `SELECT COUNT(*) FROM ${this.tableName} 
                      WHERE user_id = $1 AND tenant_id = $2 AND deleted_at IS NULL 
                      AND question_text ILIKE $3`;
    const countResult = await query(countSql, [userId, tenantId, `%${searchTerm}%`]);
    const total = parseInt(countResult.rows[0].count);

    // Get paginated results
    const dataSql = `SELECT * FROM ${this.tableName} 
                     WHERE user_id = $1 AND tenant_id = $2 AND deleted_at IS NULL 
                     AND question_text ILIKE $3
                     ORDER BY created_at DESC 
                     LIMIT $4 OFFSET $5`;
    const result = await query(dataSql, [userId, tenantId, `%${searchTerm}%`, limit, offset]);

    return {
      data: result.rows,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }
}
