import { Router } from 'express';
import {
  getStockCustomQuestions,
  getStockCustomQuestionById,
  createStockCustomQuestion,
  updateStockCustomQuestion,
  deleteStockCustomQuestion,
  createStockCustomQuestionValidation,
  updateStockCustomQuestionValidation,
  paginationValidation,
} from '../controllers/stockCustomQuestionController';
import { authenticate } from '../middleware/auth';
import { tenantIsolation } from '../middleware/tenantIsolation';

const router = Router();

// Apply authentication and tenant isolation to all routes
router.use(authenticate);
router.use(tenantIsolation);

/**
 * @swagger
 * components:
 *   schemas:
 *     StockCustomQuestion:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           format: uuid
 *         user_id:
 *           type: string
 *           format: uuid
 *         question_text:
 *           type: string
 *         tenant_id:
 *           type: string
 *           format: uuid
 *         created_at:
 *           type: string
 *           format: date-time
 *         updated_at:
 *           type: string
 *           format: date-time
 *     CreateStockCustomQuestionRequest:
 *       type: object
 *       required:
 *         - question_text
 *       properties:
 *         question_text:
 *           type: string
 *           minLength: 5
 */

/**
 * @swagger
 * /api/stock-custom-questions:
 *   get:
 *     summary: Get all stock custom questions
 *     tags: [Stock Custom Questions]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 50
 *         description: Number of items per page
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search in question text
 *     responses:
 *       200:
 *         description: Stock custom questions retrieved successfully
 */
router.get('/', paginationValidation, getStockCustomQuestions);

/**
 * @swagger
 * /api/stock-custom-questions:
 *   post:
 *     summary: Create a new stock custom question
 *     tags: [Stock Custom Questions]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CreateStockCustomQuestionRequest'
 *     responses:
 *       201:
 *         description: Stock custom question created successfully
 *       400:
 *         description: Validation error or question already exists
 */
router.post('/', createStockCustomQuestionValidation, createStockCustomQuestion);

/**
 * @swagger
 * /api/stock-custom-questions/{id}:
 *   get:
 *     summary: Get stock custom question by ID
 *     tags: [Stock Custom Questions]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Stock custom question retrieved successfully
 *       404:
 *         description: Stock custom question not found
 */
router.get('/:id', getStockCustomQuestionById);

/**
 * @swagger
 * /api/stock-custom-questions/{id}:
 *   put:
 *     summary: Update stock custom question
 *     tags: [Stock Custom Questions]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CreateStockCustomQuestionRequest'
 *     responses:
 *       200:
 *         description: Stock custom question updated successfully
 *       400:
 *         description: Validation error
 *       404:
 *         description: Stock custom question not found
 */
router.put('/:id', updateStockCustomQuestionValidation, updateStockCustomQuestion);

/**
 * @swagger
 * /api/stock-custom-questions/{id}:
 *   delete:
 *     summary: Delete stock custom question
 *     tags: [Stock Custom Questions]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Stock custom question deleted successfully
 *       404:
 *         description: Stock custom question not found
 */
router.delete('/:id', deleteStockCustomQuestion);

export default router;
