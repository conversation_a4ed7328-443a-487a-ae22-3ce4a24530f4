import { Router } from 'express';
import {
  getUserPayments,
  getPaymentById,
  createPayment,
  updatePaymentStatus,
  getActivePayment,
  getPaymentsByStatus,
  createPaymentValidation,
  updatePaymentStatusValidation,
  paginationValidation,
} from '../controllers/userPaymentController';
import { authenticate } from '../middleware/auth';
import { tenantIsolation } from '../middleware/tenantIsolation';

const router = Router();

// Apply authentication and tenant isolation to all routes
router.use(authenticate);
router.use(tenantIsolation);

/**
 * @swagger
 * components:
 *   schemas:
 *     UserPayment:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           format: uuid
 *         user_id:
 *           type: string
 *           format: uuid
 *         start_date:
 *           type: string
 *           format: date
 *         end_date:
 *           type: string
 *           format: date
 *         price:
 *           type: number
 *           format: decimal
 *         approval_status:
 *           type: string
 *           enum: [onaylandı, beklemede, reddedildi]
 *         tenant_id:
 *           type: string
 *           format: uuid
 *         created_at:
 *           type: string
 *           format: date-time
 *         updated_at:
 *           type: string
 *           format: date-time
 *     CreateUserPaymentRequest:
 *       type: object
 *       required:
 *         - start_date
 *         - end_date
 *         - price
 *       properties:
 *         start_date:
 *           type: string
 *           format: date
 *         end_date:
 *           type: string
 *           format: date
 *         price:
 *           type: number
 *           format: decimal
 *           minimum: 0.01
 *         approval_status:
 *           type: string
 *           enum: [onaylandı, beklemede, reddedildi]
 *           default: beklemede
 */

/**
 * @swagger
 * /api/user-payments:
 *   get:
 *     summary: Get user's payment history
 *     tags: [User Payments]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 10
 *         description: Number of items per page
 *     responses:
 *       200:
 *         description: Payments retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     data:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/UserPayment'
 *                     total:
 *                       type: integer
 *                     page:
 *                       type: integer
 *                     limit:
 *                       type: integer
 *                     totalPages:
 *                       type: integer
 */
router.get('/', paginationValidation, getUserPayments);

/**
 * @swagger
 * /api/user-payments/active:
 *   get:
 *     summary: Get user's active payment
 *     tags: [User Payments]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Active payment retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   $ref: '#/components/schemas/UserPayment'
 *       404:
 *         description: No active payment found
 */
router.get('/active', getActivePayment);

/**
 * @swagger
 * /api/user-payments/status/{status}:
 *   get:
 *     summary: Get payments by status
 *     tags: [User Payments]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: status
 *         required: true
 *         schema:
 *           type: string
 *           enum: [onaylandı, beklemede, reddedildi]
 *         description: Payment status
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 10
 *         description: Number of items per page
 *     responses:
 *       200:
 *         description: Payments retrieved successfully
 */
router.get('/status/:status', paginationValidation, getPaymentsByStatus);

/**
 * @swagger
 * /api/user-payments:
 *   post:
 *     summary: Create a new payment record
 *     tags: [User Payments]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CreateUserPaymentRequest'
 *     responses:
 *       201:
 *         description: Payment created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   $ref: '#/components/schemas/UserPayment'
 *       400:
 *         description: Validation error
 */
router.post('/', createPaymentValidation, createPayment);

/**
 * @swagger
 * /api/user-payments/{id}:
 *   get:
 *     summary: Get payment by ID
 *     tags: [User Payments]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Payment retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   $ref: '#/components/schemas/UserPayment'
 *       404:
 *         description: Payment not found
 */
router.get('/:id', getPaymentById);

/**
 * @swagger
 * /api/user-payments/{id}/status:
 *   put:
 *     summary: Update payment status
 *     tags: [User Payments]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - approval_status
 *             properties:
 *               approval_status:
 *                 type: string
 *                 enum: [onaylandı, beklemede, reddedildi]
 *     responses:
 *       200:
 *         description: Payment status updated successfully
 *       400:
 *         description: Validation error
 *       404:
 *         description: Payment not found
 */
router.put('/:id/status', updatePaymentStatusValidation, updatePaymentStatus);

export default router;
