import { Router } from 'express';
import { authenticate } from '../middleware/auth';
import { tenantIsolation } from '../middleware/tenantIsolation';

interface CrudRouteConfig {
  getAll: any;
  getById: any;
  create: any;
  update: any;
  delete: any;
  createValidation: any[];
  updateValidation: any[];
  paginationValidation: any[];
  entityName: string;
  entityNamePlural: string;
  tag: string;
}

export function createCrudRoutes(config: CrudRouteConfig): Router {
  const router = Router();

  // Apply authentication and tenant isolation to all routes
  router.use(authenticate);
  router.use(tenantIsolation);

  /**
   * @swagger
   * /api/{entityNamePlural}:
   *   get:
   *     summary: Get all {entityNamePlural}
   *     tags: [{tag}]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: query
   *         name: page
   *         schema:
   *           type: integer
   *           minimum: 1
   *           default: 1
   *         description: Page number
   *       - in: query
   *         name: limit
   *         schema:
   *           type: integer
   *           minimum: 1
   *           maximum: 100
   *           default: 50
   *         description: Number of items per page
   *       - in: query
   *         name: search
   *         schema:
   *           type: string
   *         description: Search term
   *     responses:
   *       200:
   *         description: {entityNamePlural} retrieved successfully
   */
  router.get('/', config.paginationValidation, config.getAll);

  /**
   * @swagger
   * /api/{entityNamePlural}:
   *   post:
   *     summary: Create a new {entityName}
   *     tags: [{tag}]
   *     security:
   *       - bearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - name
   *             properties:
   *               name:
   *                 type: string
   *                 minLength: 2
   *                 maxLength: 255
   *     responses:
   *       201:
   *         description: {entityName} created successfully
   *       400:
   *         description: Validation error or {entityName} already exists
   */
  router.post('/', config.createValidation, config.create);

  /**
   * @swagger
   * /api/{entityNamePlural}/{id}:
   *   get:
   *     summary: Get {entityName} by ID
   *     tags: [{tag}]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *     responses:
   *       200:
   *         description: {entityName} retrieved successfully
   *       404:
   *         description: {entityName} not found
   */
  router.get('/:id', config.getById);

  /**
   * @swagger
   * /api/{entityNamePlural}/{id}:
   *   put:
   *     summary: Update {entityName}
   *     tags: [{tag}]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             properties:
   *               name:
   *                 type: string
   *                 minLength: 2
   *                 maxLength: 255
   *     responses:
   *       200:
   *         description: {entityName} updated successfully
   *       400:
   *         description: Validation error
   *       404:
   *         description: {entityName} not found
   */
  router.put('/:id', config.updateValidation, config.update);

  /**
   * @swagger
   * /api/{entityNamePlural}/{id}:
   *   delete:
   *     summary: Delete {entityName}
   *     tags: [{tag}]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *     responses:
   *       200:
   *         description: {entityName} deleted successfully
   *       404:
   *         description: {entityName} not found
   */
  router.delete('/:id', config.delete);

  return router;
}
