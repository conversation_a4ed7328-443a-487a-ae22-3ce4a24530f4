import { Router } from 'express';
import { authenticate } from '../middleware/auth';
import { tenantIsolation } from '../middleware/tenantIsolation';
import { requirePermission } from '../middleware/rbac';

const router = Router();

router.use(authenticate);
router.use(tenantIsolation);

router.post('/', requirePermission('invoice:write'), (req, res) => {
  res.json({ success: true, message: 'Create invoice - Not implemented yet' });
});

router.get('/', requirePermission('invoice:read'), (req, res) => {
  res.json({ success: true, message: 'Get all invoices - Not implemented yet' });
});

router.get('/:id', requirePermission('invoice:read'), (req, res) => {
  res.json({ success: true, message: 'Get invoice by ID - Not implemented yet' });
});

router.put('/:id', requirePermission('invoice:write'), (req, res) => {
  res.json({ success: true, message: 'Update invoice - Not implemented yet' });
});

router.delete('/:id', requirePermission('invoice:delete'), (req, res) => {
  res.json({ success: true, message: 'Delete invoice - Not implemented yet' });
});

export default router;
