import { Router } from 'express';
import {
  getDashboardStats,
  getServiceTrends,
  getCashFlowTrends,
  getTopCustomers,
  dashboardValidation,
} from '../controllers/dashboardController';
import { authenticate } from '../middleware/auth';
import { tenantIsolation } from '../middleware/tenantIsolation';

const router = Router();

// Apply authentication and tenant isolation to all routes
router.use(authenticate);
router.use(tenantIsolation);

/**
 * @swagger
 * components:
 *   schemas:
 *     DashboardStats:
 *       type: object
 *       properties:
 *         services:
 *           type: object
 *           properties:
 *             total:
 *               type: integer
 *             pending:
 *               type: integer
 *             in_progress:
 *               type: integer
 *             completed:
 *               type: integer
 *             cancelled:
 *               type: integer
 *             today:
 *               type: integer
 *             this_week:
 *               type: integer
 *             this_month:
 *               type: integer
 *         customers:
 *           type: object
 *           properties:
 *             total:
 *               type: integer
 *             new_today:
 *               type: integer
 *             new_this_week:
 *               type: integer
 *             new_this_month:
 *               type: integer
 *         staff:
 *           type: object
 *           properties:
 *             total:
 *               type: integer
 *             active:
 *               type: integer
 *             inactive:
 *               type: integer
 *         cash:
 *           type: object
 *           properties:
 *             total_income:
 *               type: number
 *             total_expense:
 *               type: number
 *             net_balance:
 *               type: number
 *             today_income:
 *               type: number
 *             today_expense:
 *               type: number
 *             this_month_income:
 *               type: number
 *             this_month_expense:
 *               type: number
 *         recent_services:
 *           type: array
 *           items:
 *             $ref: '#/components/schemas/Service'
 *         recent_transactions:
 *           type: array
 *           items:
 *             $ref: '#/components/schemas/CashTransaction'
 */

/**
 * @swagger
 * /api/dashboard/stats:
 *   get:
 *     summary: Get dashboard statistics
 *     tags: [Dashboard]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Dashboard statistics retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   $ref: '#/components/schemas/DashboardStats'
 */
router.get('/stats', getDashboardStats);

/**
 * @swagger
 * /api/dashboard/service-trends:
 *   get:
 *     summary: Get service trends over time
 *     tags: [Dashboard]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: days
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 365
 *           default: 30
 *         description: Number of days to include in trends
 *     responses:
 *       200:
 *         description: Service trends retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       date:
 *                         type: string
 *                         format: date
 *                       total_services:
 *                         type: integer
 *                       completed_services:
 *                         type: integer
 */
router.get('/service-trends', dashboardValidation, getServiceTrends);

/**
 * @swagger
 * /api/dashboard/cash-flow-trends:
 *   get:
 *     summary: Get cash flow trends over time
 *     tags: [Dashboard]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: days
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 365
 *           default: 30
 *         description: Number of days to include in trends
 *     responses:
 *       200:
 *         description: Cash flow trends retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       date:
 *                         type: string
 *                         format: date
 *                       income:
 *                         type: number
 *                       expense:
 *                         type: number
 */
router.get('/cash-flow-trends', dashboardValidation, getCashFlowTrends);

/**
 * @swagger
 * /api/dashboard/top-customers:
 *   get:
 *     summary: Get top customers by service count and spending
 *     tags: [Dashboard]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 50
 *           default: 10
 *         description: Number of top customers to return
 *     responses:
 *       200:
 *         description: Top customers retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: string
 *                         format: uuid
 *                       name:
 *                         type: string
 *                       phone:
 *                         type: string
 *                       service_count:
 *                         type: integer
 *                       total_spent:
 *                         type: number
 */
router.get('/top-customers', dashboardValidation, getTopCustomers);

export default router;
