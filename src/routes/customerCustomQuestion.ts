import { Router } from 'express';
import {
  getCustomerCustomQuestions,
  getCustomerCustomQuestionById,
  createCustomerCustomQuestion,
  updateCustomerCustomQuestion,
  deleteCustomerCustomQuestion,
  createCustomerCustomQuestionValidation,
  updateCustomerCustomQuestionValidation,
  paginationValidation,
} from '../controllers/customerCustomQuestionController';
import { authenticate } from '../middleware/auth';
import { tenantIsolation } from '../middleware/tenantIsolation';

const router = Router();

// Apply authentication and tenant isolation to all routes
router.use(authenticate);
router.use(tenantIsolation);

/**
 * @swagger
 * components:
 *   schemas:
 *     CustomerCustomQuestion:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           format: uuid
 *         user_id:
 *           type: string
 *           format: uuid
 *         question_text:
 *           type: string
 *         tenant_id:
 *           type: string
 *           format: uuid
 *         created_at:
 *           type: string
 *           format: date-time
 *         updated_at:
 *           type: string
 *           format: date-time
 *     CreateCustomerCustomQuestionRequest:
 *       type: object
 *       required:
 *         - question_text
 *       properties:
 *         question_text:
 *           type: string
 *           minLength: 5
 */

/**
 * @swagger
 * /api/customer-custom-questions:
 *   get:
 *     summary: Get all customer custom questions
 *     tags: [Customer Custom Questions]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 50
 *         description: Number of items per page
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search in question text
 *     responses:
 *       200:
 *         description: Customer custom questions retrieved successfully
 */
router.get('/', paginationValidation, getCustomerCustomQuestions);

/**
 * @swagger
 * /api/customer-custom-questions:
 *   post:
 *     summary: Create a new customer custom question
 *     tags: [Customer Custom Questions]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CreateCustomerCustomQuestionRequest'
 *     responses:
 *       201:
 *         description: Customer custom question created successfully
 *       400:
 *         description: Validation error or question already exists
 */
router.post('/', createCustomerCustomQuestionValidation, createCustomerCustomQuestion);

/**
 * @swagger
 * /api/customer-custom-questions/{id}:
 *   get:
 *     summary: Get customer custom question by ID
 *     tags: [Customer Custom Questions]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Customer custom question retrieved successfully
 *       404:
 *         description: Customer custom question not found
 */
router.get('/:id', getCustomerCustomQuestionById);

/**
 * @swagger
 * /api/customer-custom-questions/{id}:
 *   put:
 *     summary: Update customer custom question
 *     tags: [Customer Custom Questions]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CreateCustomerCustomQuestionRequest'
 *     responses:
 *       200:
 *         description: Customer custom question updated successfully
 *       400:
 *         description: Validation error
 *       404:
 *         description: Customer custom question not found
 */
router.put('/:id', updateCustomerCustomQuestionValidation, updateCustomerCustomQuestion);

/**
 * @swagger
 * /api/customer-custom-questions/{id}:
 *   delete:
 *     summary: Delete customer custom question
 *     tags: [Customer Custom Questions]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Customer custom question deleted successfully
 *       404:
 *         description: Customer custom question not found
 */
router.delete('/:id', deleteCustomerCustomQuestion);

export default router;
