import { Router } from 'express';
import {
  getSmsSettings,
  createSmsSettings,
  updateSmsSettings,
  deleteSmsSettings,
  processSmsTemplate,
  createSmsSettingsValidation,
  updateSmsSettingsValidation,
  processSmsTemplateValidation,
} from '../controllers/smsSettingsController';
import { authenticate } from '../middleware/auth';
import { tenantIsolation } from '../middleware/tenantIsolation';

const router = Router();

// Apply authentication and tenant isolation to all routes
router.use(authenticate);
router.use(tenantIsolation);

/**
 * @swagger
 * components:
 *   schemas:
 *     SmsSettings:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           format: uuid
 *         user_id:
 *           type: string
 *           format: uuid
 *         sender_company:
 *           type: string
 *         message_header:
 *           type: string
 *         username:
 *           type: string
 *         sms_template:
 *           type: string
 *         tenant_id:
 *           type: string
 *           format: uuid
 *         created_at:
 *           type: string
 *           format: date-time
 *         updated_at:
 *           type: string
 *           format: date-time
 *     CreateSmsSettingsRequest:
 *       type: object
 *       required:
 *         - sender_company
 *         - message_header
 *         - username
 *         - password
 *         - sms_template
 *       properties:
 *         sender_company:
 *           type: string
 *           minLength: 2
 *           maxLength: 255
 *         message_header:
 *           type: string
 *           minLength: 1
 *           maxLength: 100
 *         username:
 *           type: string
 *           minLength: 1
 *           maxLength: 100
 *         password:
 *           type: string
 *           minLength: 6
 *         sms_template:
 *           type: string
 *           minLength: 10
 *           description: Must contain [[MUSTERIADI]], [[MARKAADI]], and [[TARIH]] placeholders
 */

/**
 * @swagger
 * /api/sms-settings:
 *   get:
 *     summary: Get user's SMS settings
 *     tags: [SMS Settings]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: SMS settings retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   $ref: '#/components/schemas/SmsSettings'
 *       404:
 *         description: SMS settings not found
 */
router.get('/', getSmsSettings);

/**
 * @swagger
 * /api/sms-settings:
 *   post:
 *     summary: Create SMS settings
 *     tags: [SMS Settings]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CreateSmsSettingsRequest'
 *           example:
 *             sender_company: "My Company"
 *             message_header: "SERVICE"
 *             username: "sms_user"
 *             password: "sms_password"
 *             sms_template: "Sayın [[MUSTERIADI]], [[MARKAADI]] cihazınızın servisi [[TARIH]] tarihinde tamamlanmıştır."
 *     responses:
 *       201:
 *         description: SMS settings created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   $ref: '#/components/schemas/SmsSettings'
 *       400:
 *         description: Validation error or SMS settings already exist
 */
router.post('/', createSmsSettingsValidation, createSmsSettings);

/**
 * @swagger
 * /api/sms-settings:
 *   put:
 *     summary: Update SMS settings
 *     tags: [SMS Settings]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CreateSmsSettingsRequest'
 *     responses:
 *       200:
 *         description: SMS settings updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   $ref: '#/components/schemas/SmsSettings'
 *       400:
 *         description: Validation error
 *       404:
 *         description: SMS settings not found
 */
router.put('/', updateSmsSettingsValidation, updateSmsSettings);

/**
 * @swagger
 * /api/sms-settings:
 *   delete:
 *     summary: Delete SMS settings
 *     tags: [SMS Settings]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: SMS settings deleted successfully
 *       404:
 *         description: SMS settings not found
 */
router.delete('/', deleteSmsSettings);

/**
 * @swagger
 * /api/sms-settings/process-template:
 *   post:
 *     summary: Process SMS template with dynamic values
 *     tags: [SMS Settings]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - customer_name
 *               - brand_name
 *               - date
 *             properties:
 *               customer_name:
 *                 type: string
 *                 description: Customer name to replace [[MUSTERIADI]]
 *               brand_name:
 *                 type: string
 *                 description: Brand name to replace [[MARKAADI]]
 *               date:
 *                 type: string
 *                 description: Date to replace [[TARIH]]
 *           example:
 *             customer_name: "Ahmet Yılmaz"
 *             brand_name: "iPhone"
 *             date: "2024-01-15"
 *     responses:
 *       200:
 *         description: SMS template processed successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     processed_message:
 *                       type: string
 *                       description: The processed SMS message with replaced placeholders
 *       400:
 *         description: Validation error
 *       404:
 *         description: SMS settings not found
 */
router.post('/process-template', processSmsTemplateValidation, processSmsTemplate);

export default router;
