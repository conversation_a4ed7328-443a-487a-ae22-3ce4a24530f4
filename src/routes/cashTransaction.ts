import { Router } from 'express';
import {
  getCashTransactions,
  getCashTransactionById,
  createCashTransaction,
  updateCashTransaction,
  deleteCashTransaction,
  getCashSummary,
  getTransactionsByCategory,
  createCashTransactionValidation,
  updateCashTransactionValidation,
  paginationValidation,
} from '../controllers/cashTransactionController';
import { authenticate } from '../middleware/auth';
import { tenantIsolation } from '../middleware/tenantIsolation';

const router = Router();

// Apply authentication and tenant isolation to all routes
router.use(authenticate);
router.use(tenantIsolation);

/**
 * @swagger
 * components:
 *   schemas:
 *     CashTransaction:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           format: uuid
 *         user_id:
 *           type: string
 *           format: uuid
 *         amount:
 *           type: number
 *         transaction_type:
 *           type: string
 *           enum: [income, expense]
 *         category:
 *           type: string
 *         description:
 *           type: string
 *         reference_id:
 *           type: string
 *           format: uuid
 *         reference_type:
 *           type: string
 *         transaction_date:
 *           type: string
 *           format: date
 *         tenant_id:
 *           type: string
 *           format: uuid
 *         created_at:
 *           type: string
 *           format: date-time
 *         updated_at:
 *           type: string
 *           format: date-time
 *     CreateCashTransactionRequest:
 *       type: object
 *       required:
 *         - amount
 *         - transaction_type
 *         - description
 *       properties:
 *         amount:
 *           type: number
 *           minimum: 0.01
 *         transaction_type:
 *           type: string
 *           enum: [income, expense]
 *         category:
 *           type: string
 *           maxLength: 100
 *         description:
 *           type: string
 *           minLength: 3
 *           maxLength: 1000
 *         reference_id:
 *           type: string
 *           format: uuid
 *         reference_type:
 *           type: string
 *           maxLength: 50
 *         transaction_date:
 *           type: string
 *           format: date
 */

/**
 * @swagger
 * /api/cash-transactions:
 *   get:
 *     summary: Get all cash transactions
 *     tags: [Cash Transactions]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 50
 *         description: Number of items per page
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search term
 *       - in: query
 *         name: transaction_type
 *         schema:
 *           type: string
 *           enum: [income, expense]
 *         description: Filter by transaction type
 *       - in: query
 *         name: category
 *         schema:
 *           type: string
 *         description: Filter by category
 *       - in: query
 *         name: start_date
 *         schema:
 *           type: string
 *           format: date
 *         description: Start date filter
 *       - in: query
 *         name: end_date
 *         schema:
 *           type: string
 *           format: date
 *         description: End date filter
 *     responses:
 *       200:
 *         description: Cash transactions retrieved successfully
 */
router.get('/', paginationValidation, getCashTransactions);

/**
 * @swagger
 * /api/cash-transactions/summary:
 *   get:
 *     summary: Get cash summary
 *     tags: [Cash Transactions]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: start_date
 *         schema:
 *           type: string
 *           format: date
 *         description: Start date filter
 *       - in: query
 *         name: end_date
 *         schema:
 *           type: string
 *           format: date
 *         description: End date filter
 *     responses:
 *       200:
 *         description: Cash summary retrieved successfully
 */
router.get('/summary', getCashSummary);

/**
 * @swagger
 * /api/cash-transactions/categories:
 *   get:
 *     summary: Get transactions grouped by category
 *     tags: [Cash Transactions]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Transactions by category retrieved successfully
 */
router.get('/categories', getTransactionsByCategory);

/**
 * @swagger
 * /api/cash-transactions:
 *   post:
 *     summary: Create a new cash transaction
 *     tags: [Cash Transactions]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CreateCashTransactionRequest'
 *     responses:
 *       201:
 *         description: Cash transaction created successfully
 *       400:
 *         description: Validation error
 */
router.post('/', createCashTransactionValidation, createCashTransaction);

/**
 * @swagger
 * /api/cash-transactions/{id}:
 *   get:
 *     summary: Get cash transaction by ID
 *     tags: [Cash Transactions]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Cash transaction retrieved successfully
 *       404:
 *         description: Cash transaction not found
 */
router.get('/:id', getCashTransactionById);

/**
 * @swagger
 * /api/cash-transactions/{id}:
 *   put:
 *     summary: Update cash transaction
 *     tags: [Cash Transactions]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CreateCashTransactionRequest'
 *     responses:
 *       200:
 *         description: Cash transaction updated successfully
 *       400:
 *         description: Validation error
 *       404:
 *         description: Cash transaction not found
 */
router.put('/:id', updateCashTransactionValidation, updateCashTransaction);

/**
 * @swagger
 * /api/cash-transactions/{id}:
 *   delete:
 *     summary: Delete cash transaction
 *     tags: [Cash Transactions]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Cash transaction deleted successfully
 *       404:
 *         description: Cash transaction not found
 */
router.delete('/:id', deleteCashTransaction);

export default router;
