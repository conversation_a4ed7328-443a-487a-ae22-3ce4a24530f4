import { Router } from 'express';
import {
  getPersonnelCustomQuestions,
  getPersonnelCustomQuestionById,
  createPersonnelCustomQuestion,
  updatePersonnelCustomQuestion,
  deletePersonnelCustomQuestion,
  createPersonnelCustomQuestionValidation,
  updatePersonnelCustomQuestionValidation,
  paginationValidation,
} from '../controllers/personnelCustomQuestionController';
import { authenticate } from '../middleware/auth';
import { tenantIsolation } from '../middleware/tenantIsolation';

const router = Router();

// Apply authentication and tenant isolation to all routes
router.use(authenticate);
router.use(tenantIsolation);

/**
 * @swagger
 * components:
 *   schemas:
 *     PersonnelCustomQuestion:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           format: uuid
 *         user_id:
 *           type: string
 *           format: uuid
 *         question_text:
 *           type: string
 *         tenant_id:
 *           type: string
 *           format: uuid
 *         created_at:
 *           type: string
 *           format: date-time
 *         updated_at:
 *           type: string
 *           format: date-time
 *     CreatePersonnelCustomQuestionRequest:
 *       type: object
 *       required:
 *         - question_text
 *       properties:
 *         question_text:
 *           type: string
 *           minLength: 5
 */

/**
 * @swagger
 * /api/personnel-custom-questions:
 *   get:
 *     summary: Get all personnel custom questions
 *     tags: [Personnel Custom Questions]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 50
 *         description: Number of items per page
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search in question text
 *     responses:
 *       200:
 *         description: Personnel custom questions retrieved successfully
 */
router.get('/', paginationValidation, getPersonnelCustomQuestions);

/**
 * @swagger
 * /api/personnel-custom-questions:
 *   post:
 *     summary: Create a new personnel custom question
 *     tags: [Personnel Custom Questions]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CreatePersonnelCustomQuestionRequest'
 *     responses:
 *       201:
 *         description: Personnel custom question created successfully
 *       400:
 *         description: Validation error or question already exists
 */
router.post('/', createPersonnelCustomQuestionValidation, createPersonnelCustomQuestion);

/**
 * @swagger
 * /api/personnel-custom-questions/{id}:
 *   get:
 *     summary: Get personnel custom question by ID
 *     tags: [Personnel Custom Questions]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Personnel custom question retrieved successfully
 *       404:
 *         description: Personnel custom question not found
 */
router.get('/:id', getPersonnelCustomQuestionById);

/**
 * @swagger
 * /api/personnel-custom-questions/{id}:
 *   put:
 *     summary: Update personnel custom question
 *     tags: [Personnel Custom Questions]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CreatePersonnelCustomQuestionRequest'
 *     responses:
 *       200:
 *         description: Personnel custom question updated successfully
 *       400:
 *         description: Validation error
 *       404:
 *         description: Personnel custom question not found
 */
router.put('/:id', updatePersonnelCustomQuestionValidation, updatePersonnelCustomQuestion);

/**
 * @swagger
 * /api/personnel-custom-questions/{id}:
 *   delete:
 *     summary: Delete personnel custom question
 *     tags: [Personnel Custom Questions]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Personnel custom question deleted successfully
 *       404:
 *         description: Personnel custom question not found
 */
router.delete('/:id', deletePersonnelCustomQuestion);

export default router;
