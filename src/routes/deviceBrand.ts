import { createCrudRoutes } from './baseCrudRoute';
import {
  getDeviceBrands,
  getDeviceBrandById,
  createDeviceBrand,
  updateDeviceBrand,
  deleteDeviceBrand,
  createDeviceBrandValidation,
  updateDeviceBrandValidation,
  paginationValidation,
} from '../controllers/deviceBrandController';

const router = createCrudRoutes({
  getAll: getDeviceBrands,
  getById: getDeviceBrandById,
  create: createDeviceBrand,
  update: updateDeviceBrand,
  delete: deleteDeviceBrand,
  createValidation: createDeviceBrandValidation,
  updateValidation: updateDeviceBrandValidation,
  paginationValidation: paginationValidation,
  entityName: 'device brand',
  entityNamePlural: 'device-brands',
  tag: 'Device Brands',
});

export default router;
