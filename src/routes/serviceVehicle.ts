import { Router } from 'express';
import {
  getServiceVehicles,
  getServiceVehicleById,
  createServiceVehicle,
  updateServiceVehicle,
  deleteServiceVehicle,
  createServiceVehicleValidation,
  updateServiceVehicleValidation,
  paginationValidation,
} from '../controllers/serviceVehicleController';
import { authenticate } from '../middleware/auth';
import { tenantIsolation } from '../middleware/tenantIsolation';

const router = Router();

// Apply authentication and tenant isolation to all routes
router.use(authenticate);
router.use(tenantIsolation);

/**
 * @swagger
 * components:
 *   schemas:
 *     ServiceVehicle:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           format: uuid
 *         user_id:
 *           type: string
 *           format: uuid
 *         plate_number:
 *           type: string
 *         tenant_id:
 *           type: string
 *           format: uuid
 *         created_at:
 *           type: string
 *           format: date-time
 *         updated_at:
 *           type: string
 *           format: date-time
 *     CreateServiceVehicleRequest:
 *       type: object
 *       required:
 *         - plate_number
 *       properties:
 *         plate_number:
 *           type: string
 *           minLength: 2
 *           maxLength: 20
 *           pattern: '^[A-Z0-9\s-]+$'
 */

/**
 * @swagger
 * /api/service-vehicles:
 *   get:
 *     summary: Get all service vehicles
 *     tags: [Service Vehicles]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 50
 *         description: Number of items per page
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search by plate number
 *     responses:
 *       200:
 *         description: Service vehicles retrieved successfully
 */
router.get('/', paginationValidation, getServiceVehicles);

/**
 * @swagger
 * /api/service-vehicles:
 *   post:
 *     summary: Create a new service vehicle
 *     tags: [Service Vehicles]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CreateServiceVehicleRequest'
 *     responses:
 *       201:
 *         description: Service vehicle created successfully
 *       400:
 *         description: Validation error or plate number already exists
 */
router.post('/', createServiceVehicleValidation, createServiceVehicle);

/**
 * @swagger
 * /api/service-vehicles/{id}:
 *   get:
 *     summary: Get service vehicle by ID
 *     tags: [Service Vehicles]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Service vehicle retrieved successfully
 *       404:
 *         description: Service vehicle not found
 */
router.get('/:id', getServiceVehicleById);

/**
 * @swagger
 * /api/service-vehicles/{id}:
 *   put:
 *     summary: Update service vehicle
 *     tags: [Service Vehicles]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CreateServiceVehicleRequest'
 *     responses:
 *       200:
 *         description: Service vehicle updated successfully
 *       400:
 *         description: Validation error
 *       404:
 *         description: Service vehicle not found
 */
router.put('/:id', updateServiceVehicleValidation, updateServiceVehicle);

/**
 * @swagger
 * /api/service-vehicles/{id}:
 *   delete:
 *     summary: Delete service vehicle
 *     tags: [Service Vehicles]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Service vehicle deleted successfully
 *       404:
 *         description: Service vehicle not found
 */
router.delete('/:id', deleteServiceVehicle);

export default router;
