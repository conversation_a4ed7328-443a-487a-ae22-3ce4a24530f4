import { createCrudRoutes } from './baseCrudRoute';
import {
  getServiceSources,
  getServiceSourceById,
  createServiceSource,
  updateServiceSource,
  deleteServiceSource,
  createServiceSourceValidation,
  updateServiceSourceValidation,
  paginationValidation,
} from '../controllers/serviceSourceController';

const router = createCrudRoutes({
  getAll: getServiceSources,
  getById: getServiceSourceById,
  create: createServiceSource,
  update: updateServiceSource,
  delete: deleteServiceSource,
  createValidation: createServiceSourceValidation,
  updateValidation: updateServiceSourceValidation,
  paginationValidation: paginationValidation,
  entityName: 'service source',
  entityNamePlural: 'service-sources',
  tag: 'Service Sources',
});

export default router;
