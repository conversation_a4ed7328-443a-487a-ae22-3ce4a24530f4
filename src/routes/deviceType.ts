import { createCrudRoutes } from './baseCrudRoute';
import {
  getDeviceTypes,
  getDeviceTypeById,
  createDeviceType,
  updateDeviceType,
  deleteDeviceType,
  createDeviceTypeValidation,
  updateDeviceTypeValidation,
  paginationValidation,
} from '../controllers/deviceTypeController';

const router = createCrudRoutes({
  getAll: getDeviceTypes,
  getById: getDeviceTypeById,
  create: createDeviceType,
  update: updateDeviceType,
  delete: deleteDeviceType,
  createValidation: createDeviceTypeValidation,
  updateValidation: updateDeviceTypeValidation,
  paginationValidation: paginationValidation,
  entityName: 'device type',
  entityNamePlural: 'device-types',
  tag: 'Device Types',
});

export default router;
