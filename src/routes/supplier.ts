import { createCrudRoutes } from './baseCrudRoute';
import {
  getSuppliers,
  getSupplierById,
  createSupplier,
  updateSupplier,
  deleteSupplier,
  createSupplierValidation,
  updateSupplierValidation,
  paginationValidation,
} from '../controllers/supplierController';

const router = createCrudRoutes({
  getAll: getSuppliers,
  getById: getSupplierById,
  create: createSupplier,
  update: updateSupplier,
  delete: deleteSupplier,
  createValidation: createSupplierValidation,
  updateValidation: updateSupplierValidation,
  paginationValidation: paginationValidation,
  entityName: 'supplier',
  entityNamePlural: 'suppliers',
  tag: 'Suppliers',
});

export default router;
