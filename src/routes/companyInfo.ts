import { Router } from 'express';
import {
  getCompanyInfo,
  createCompanyInfo,
  updateCompanyInfo,
  deleteCompanyInfo,
  createCompanyInfoValidation,
  updateCompanyInfoValidation,
} from '../controllers/companyInfoController';
import { authenticate } from '../middleware/auth';
import { tenantIsolation } from '../middleware/tenantIsolation';

const router = Router();

// Apply authentication and tenant isolation to all routes
router.use(authenticate);
router.use(tenantIsolation);

/**
 * @swagger
 * components:
 *   schemas:
 *     CompanyInfo:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           format: uuid
 *         user_id:
 *           type: string
 *           format: uuid
 *         company_name:
 *           type: string
 *         phone1:
 *           type: string
 *         phone2:
 *           type: string
 *         city:
 *           type: string
 *         district:
 *           type: string
 *         address:
 *           type: string
 *         email:
 *           type: string
 *           format: email
 *         website:
 *           type: string
 *           format: uri
 *         tax_number:
 *           type: string
 *         tax_office:
 *           type: string
 *         tenant_id:
 *           type: string
 *           format: uuid
 *         created_at:
 *           type: string
 *           format: date-time
 *         updated_at:
 *           type: string
 *           format: date-time
 *     CreateCompanyInfoRequest:
 *       type: object
 *       required:
 *         - company_name
 *         - phone1
 *         - city
 *         - district
 *         - address
 *       properties:
 *         company_name:
 *           type: string
 *           minLength: 2
 *           maxLength: 255
 *         phone1:
 *           type: string
 *           maxLength: 50
 *         phone2:
 *           type: string
 *           maxLength: 50
 *         city:
 *           type: string
 *           maxLength: 100
 *         district:
 *           type: string
 *           maxLength: 100
 *         address:
 *           type: string
 *         email:
 *           type: string
 *           format: email
 *           maxLength: 255
 *         website:
 *           type: string
 *           format: uri
 *           maxLength: 255
 *         tax_number:
 *           type: string
 *           maxLength: 50
 *         tax_office:
 *           type: string
 *           maxLength: 100
 */

/**
 * @swagger
 * /api/company-info:
 *   get:
 *     summary: Get user's company information
 *     tags: [Company Info]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Company info retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   $ref: '#/components/schemas/CompanyInfo'
 *       404:
 *         description: Company info not found
 */
router.get('/', getCompanyInfo);

/**
 * @swagger
 * /api/company-info:
 *   post:
 *     summary: Create company information
 *     tags: [Company Info]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CreateCompanyInfoRequest'
 *     responses:
 *       201:
 *         description: Company info created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   $ref: '#/components/schemas/CompanyInfo'
 *       400:
 *         description: Validation error or company info already exists
 */
router.post('/', createCompanyInfoValidation, createCompanyInfo);

/**
 * @swagger
 * /api/company-info:
 *   put:
 *     summary: Update company information
 *     tags: [Company Info]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CreateCompanyInfoRequest'
 *     responses:
 *       200:
 *         description: Company info updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   $ref: '#/components/schemas/CompanyInfo'
 *       400:
 *         description: Validation error
 *       404:
 *         description: Company info not found
 */
router.put('/', updateCompanyInfoValidation, updateCompanyInfo);

/**
 * @swagger
 * /api/company-info:
 *   delete:
 *     summary: Delete company information
 *     tags: [Company Info]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Company info deleted successfully
 *       404:
 *         description: Company info not found
 */
router.delete('/', deleteCompanyInfo);

export default router;
