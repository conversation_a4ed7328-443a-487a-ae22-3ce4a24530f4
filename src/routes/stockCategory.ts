import { Router } from 'express';
import {
  getStockCategories,
  getStockCategoryHierarchy,
  getStockCategoryChildren,
  getStockCategoryById,
  createStockCategory,
  updateStockCategory,
  deleteStockCategory,
  createStockCategoryValidation,
  updateStockCategoryValidation,
  paginationValidation,
} from '../controllers/stockCategoryController';
import { authenticate } from '../middleware/auth';
import { tenantIsolation } from '../middleware/tenantIsolation';

const router = Router();

// Apply authentication and tenant isolation to all routes
router.use(authenticate);
router.use(tenantIsolation);

/**
 * @swagger
 * components:
 *   schemas:
 *     StockCategory:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           format: uuid
 *         user_id:
 *           type: string
 *           format: uuid
 *         name:
 *           type: string
 *         parent_id:
 *           type: string
 *           format: uuid
 *           nullable: true
 *         tenant_id:
 *           type: string
 *           format: uuid
 *         created_at:
 *           type: string
 *           format: date-time
 *         updated_at:
 *           type: string
 *           format: date-time
 *     CreateStockCategoryRequest:
 *       type: object
 *       required:
 *         - name
 *       properties:
 *         name:
 *           type: string
 *           minLength: 2
 *           maxLength: 255
 *         parent_id:
 *           type: string
 *           format: uuid
 *           nullable: true
 */

/**
 * @swagger
 * /api/stock-categories:
 *   get:
 *     summary: Get all stock categories
 *     tags: [Stock Categories]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 50
 *         description: Number of items per page
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search term
 *     responses:
 *       200:
 *         description: Stock categories retrieved successfully
 */
router.get('/', paginationValidation, getStockCategories);

/**
 * @swagger
 * /api/stock-categories/hierarchy:
 *   get:
 *     summary: Get stock categories in hierarchical structure
 *     tags: [Stock Categories]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Category hierarchy retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/StockCategory'
 */
router.get('/hierarchy', getStockCategoryHierarchy);

/**
 * @swagger
 * /api/stock-categories:
 *   post:
 *     summary: Create a new stock category
 *     tags: [Stock Categories]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CreateStockCategoryRequest'
 *     responses:
 *       201:
 *         description: Stock category created successfully
 *       400:
 *         description: Validation error or category already exists
 */
router.post('/', createStockCategoryValidation, createStockCategory);

/**
 * @swagger
 * /api/stock-categories/{id}/children:
 *   get:
 *     summary: Get child categories of a specific category
 *     tags: [Stock Categories]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Child categories retrieved successfully
 */
router.get('/:id/children', getStockCategoryChildren);

/**
 * @swagger
 * /api/stock-categories/{id}:
 *   get:
 *     summary: Get stock category by ID
 *     tags: [Stock Categories]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Stock category retrieved successfully
 *       404:
 *         description: Stock category not found
 */
router.get('/:id', getStockCategoryById);

/**
 * @swagger
 * /api/stock-categories/{id}:
 *   put:
 *     summary: Update stock category
 *     tags: [Stock Categories]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CreateStockCategoryRequest'
 *     responses:
 *       200:
 *         description: Stock category updated successfully
 *       400:
 *         description: Validation error or circular reference
 *       404:
 *         description: Stock category not found
 */
router.put('/:id', updateStockCategoryValidation, updateStockCategory);

/**
 * @swagger
 * /api/stock-categories/{id}:
 *   delete:
 *     summary: Delete stock category
 *     tags: [Stock Categories]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Stock category deleted successfully
 *       400:
 *         description: Cannot delete category that has subcategories
 *       404:
 *         description: Stock category not found
 */
router.delete('/:id', deleteStockCategory);

export default router;
