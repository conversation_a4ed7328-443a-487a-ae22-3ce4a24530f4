import { Router } from 'express';
import {
  getCustomerCategories,
  getCustomerCategoryHierarchy,
  getCustomerCategoryChildren,
  getCustomerCategoryById,
  createCustomerCategory,
  updateCustomerCategory,
  deleteCustomerCategory,
  createCustomerCategoryValidation,
  updateCustomerCategoryValidation,
  paginationValidation,
} from '../controllers/customerCategoryController';
import { authenticate } from '../middleware/auth';
import { tenantIsolation } from '../middleware/tenantIsolation';

const router = Router();

// Apply authentication and tenant isolation to all routes
router.use(authenticate);
router.use(tenantIsolation);

/**
 * @swagger
 * components:
 *   schemas:
 *     CustomerCategory:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           format: uuid
 *         user_id:
 *           type: string
 *           format: uuid
 *         name:
 *           type: string
 *         parent_id:
 *           type: string
 *           format: uuid
 *           nullable: true
 *         tenant_id:
 *           type: string
 *           format: uuid
 *         created_at:
 *           type: string
 *           format: date-time
 *         updated_at:
 *           type: string
 *           format: date-time
 *     CreateCustomerCategoryRequest:
 *       type: object
 *       required:
 *         - name
 *       properties:
 *         name:
 *           type: string
 *           minLength: 2
 *           maxLength: 255
 *         parent_id:
 *           type: string
 *           format: uuid
 *           nullable: true
 */

/**
 * @swagger
 * /api/customer-categories:
 *   get:
 *     summary: Get all customer categories
 *     tags: [Customer Categories]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 50
 *         description: Number of items per page
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search term
 *     responses:
 *       200:
 *         description: Customer categories retrieved successfully
 */
router.get('/', paginationValidation, getCustomerCategories);

/**
 * @swagger
 * /api/customer-categories/hierarchy:
 *   get:
 *     summary: Get customer categories in hierarchical structure
 *     tags: [Customer Categories]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Category hierarchy retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/CustomerCategory'
 */
router.get('/hierarchy', getCustomerCategoryHierarchy);

/**
 * @swagger
 * /api/customer-categories:
 *   post:
 *     summary: Create a new customer category
 *     tags: [Customer Categories]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CreateCustomerCategoryRequest'
 *     responses:
 *       201:
 *         description: Customer category created successfully
 *       400:
 *         description: Validation error or category already exists
 */
router.post('/', createCustomerCategoryValidation, createCustomerCategory);

/**
 * @swagger
 * /api/customer-categories/{id}/children:
 *   get:
 *     summary: Get child categories of a specific category
 *     tags: [Customer Categories]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Child categories retrieved successfully
 */
router.get('/:id/children', getCustomerCategoryChildren);

/**
 * @swagger
 * /api/customer-categories/{id}:
 *   get:
 *     summary: Get customer category by ID
 *     tags: [Customer Categories]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Customer category retrieved successfully
 *       404:
 *         description: Customer category not found
 */
router.get('/:id', getCustomerCategoryById);

/**
 * @swagger
 * /api/customer-categories/{id}:
 *   put:
 *     summary: Update customer category
 *     tags: [Customer Categories]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CreateCustomerCategoryRequest'
 *     responses:
 *       200:
 *         description: Customer category updated successfully
 *       400:
 *         description: Validation error or circular reference
 *       404:
 *         description: Customer category not found
 */
router.put('/:id', updateCustomerCategoryValidation, updateCustomerCategory);

/**
 * @swagger
 * /api/customer-categories/{id}:
 *   delete:
 *     summary: Delete customer category
 *     tags: [Customer Categories]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Customer category deleted successfully
 *       400:
 *         description: Cannot delete category that has subcategories
 *       404:
 *         description: Customer category not found
 */
router.delete('/:id', deleteCustomerCategory);

export default router;
