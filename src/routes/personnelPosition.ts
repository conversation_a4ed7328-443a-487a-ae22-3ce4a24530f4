import { createCrudRoutes } from './baseCrudRoute';
import {
  getPersonnelPositions,
  getPersonnelPositionById,
  createPersonnelPosition,
  updatePersonnelPosition,
  deletePersonnelPosition,
  createPersonnelPositionValidation,
  updatePersonnelPositionValidation,
  paginationValidation,
} from '../controllers/personnelPositionController';

const router = createCrudRoutes({
  getAll: getPersonnelPositions,
  getById: getPersonnelPositionById,
  create: createPersonnelPosition,
  update: updatePersonnelPosition,
  delete: deletePersonnelPosition,
  createValidation: createPersonnelPositionValidation,
  updateValidation: updatePersonnelPositionValidation,
  paginationValidation: paginationValidation,
  entityName: 'personnel position',
  entityNamePlural: 'personnel-positions',
  tag: 'Personnel Positions',
});

export default router;
