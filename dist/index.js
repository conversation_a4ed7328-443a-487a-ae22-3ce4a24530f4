"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const helmet_1 = __importDefault(require("helmet"));
const morgan_1 = __importDefault(require("morgan"));
const dotenv_1 = __importDefault(require("dotenv"));
const path_1 = __importDefault(require("path"));
const swagger_jsdoc_1 = __importDefault(require("swagger-jsdoc"));
const swagger_ui_express_1 = __importDefault(require("swagger-ui-express"));
const errorHandler_1 = require("./middleware/errorHandler");
const notFound_1 = require("./middleware/notFound");
const connection_1 = require("./database/connection");
// Import routes
const auth_1 = __importDefault(require("./routes/auth"));
const tenant_1 = __importDefault(require("./routes/tenant"));
const user_1 = __importDefault(require("./routes/user"));
const customer_1 = __importDefault(require("./routes/customer"));
const device_1 = __importDefault(require("./routes/device"));
const serviceRequest_1 = __importDefault(require("./routes/serviceRequest"));
const serviceNote_1 = __importDefault(require("./routes/serviceNote"));
const invoice_1 = __importDefault(require("./routes/invoice"));
const stockItem_1 = __importDefault(require("./routes/stockItem"));
const payment_1 = __importDefault(require("./routes/payment"));
const reminder_1 = __importDefault(require("./routes/reminder"));
const message_1 = __importDefault(require("./routes/message"));
// Load environment variables
dotenv_1.default.config();
const app = (0, express_1.default)();
const PORT = process.env.PORT || 3000;
const BASE_URL = process.env.BASE_URL || `http://localhost:${PORT}`;

// Swagger configuration
const swaggerOptions = {
    definition: {
        openapi: '3.0.0',
        info: {
            title: 'Servisbul API',
            version: '1.0.0',
            description: 'Multitenant Technical Service Management System API',
        },
        servers: [
            {
                url: BASE_URL,
                description: 'Development server',
            },
        ],
        components: {
            securitySchemes: {
                bearerAuth: {
                    type: 'http',
                    scheme: 'bearer',
                    bearerFormat: 'JWT',
                },
            },
        },
        security: [
            {
                bearerAuth: [],
            },
        ],
    },
    apis: ['./src/routes/*.ts', './src/models/*.ts'],
};
const specs = (0, swagger_jsdoc_1.default)(swaggerOptions);
// Middleware
app.use((0, helmet_1.default)());
app.use((0, cors_1.default)({
    origin: process.env.CORS_ORIGIN || 'http://localhost:3000',
    credentials: true,
}));
app.use((0, morgan_1.default)('combined'));
app.use(express_1.default.json({ limit: '10mb' }));
app.use(express_1.default.urlencoded({ extended: true, limit: '10mb' }));
// Serve uploaded files
app.use('/uploads', express_1.default.static(path_1.default.join(__dirname, '../uploads')));
// API Documentation
app.use('/api-docs', swagger_ui_express_1.default.serve, swagger_ui_express_1.default.setup(specs));
// Health check
app.get('/health', (req, res) => {
    res.json({ status: 'OK', timestamp: new Date().toISOString() });
});
// API Routes
app.use('/api/auth', auth_1.default);
app.use('/api/tenants', tenant_1.default);
app.use('/api/users', user_1.default);
app.use('/api/customers', customer_1.default);
app.use('/api/devices', device_1.default);
app.use('/api/service-requests', serviceRequest_1.default);
app.use('/api/service-notes', serviceNote_1.default);
app.use('/api/invoices', invoice_1.default);
app.use('/api/stock-items', stockItem_1.default);
app.use('/api/payments', payment_1.default);
app.use('/api/reminders', reminder_1.default);
app.use('/api/messages', message_1.default);
// Error handling middleware
app.use(notFound_1.notFound);
app.use(errorHandler_1.errorHandler);
// Start server
async function startServer() {
    try {
        // Connect to database
        await (0, connection_1.connectDatabase)();
        console.log('✅ Database connected successfully');
        app.listen(PORT, () => {
            console.log(`🚀 Server running on port ${PORT}`);
            console.log(`📚 API Documentation: http://localhost:${PORT}/api-docs`);
            console.log(`🏥 Health Check: http://localhost:${PORT}/health`);
        });
    }
    catch (error) {
        console.error('❌ Failed to start server:', error);
        process.exit(1);
    }
}
startServer();
//# sourceMappingURL=index.js.map