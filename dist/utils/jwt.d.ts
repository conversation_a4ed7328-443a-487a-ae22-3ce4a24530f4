import { User, Tenant } from '../types/models';
export interface JwtPayload {
    userId: string;
    tenantId: string;
    role: string;
    email: string;
}
export declare const generateToken: (user: User, tenant: Tenant) => string;
export declare const verifyToken: (token: string) => JwtPayload;
export declare const extractTokenFromHeader: (authHeader: string) => string;
//# sourceMappingURL=jwt.d.ts.map