import { Customer, PaginatedResponse } from '../types/models';
export interface CreateCustomerRequest {
    name: string;
    phone: string;
    email?: string;
    address?: string;
}
export declare class CustomerService {
    createCustomer(customerData: CreateCustomerRequest, tenantId: string, createdBy: string): Promise<Customer>;
    getCustomerById(id: string, tenantId: string): Promise<Customer | null>;
    getAllCustomers(tenantId: string, page?: number, limit?: number, search?: string): Promise<PaginatedResponse<Customer>>;
    updateCustomer(id: string, updateData: Partial<CreateCustomerRequest>, tenantId: string): Promise<Customer>;
    deleteCustomer(id: string, tenantId: string): Promise<void>;
    getCustomerDevices(customerId: string, tenantId: string): Promise<any[]>;
}
//# sourceMappingURL=customerService.d.ts.map