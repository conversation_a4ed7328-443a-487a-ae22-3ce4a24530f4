"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthService = void 0;
const bcryptjs_1 = __importDefault(require("bcryptjs"));
const connection_1 = require("../database/connection");
const jwt_1 = require("../utils/jwt");
class AuthService {
    async login(loginData) {
        const { email, password } = loginData;
        // Find user with tenant information
        const userResult = await (0, connection_1.query)(`
      SELECT u.*, t.name as tenant_name, t.slug as tenant_slug, t.logo as tenant_logo, t.is_active as tenant_active
      FROM users u
      JOIN tenants t ON u.tenant_id = t.id
      WHERE u.email = $1 AND u.is_active = true AND t.is_active = true
    `, [email]);
        if (userResult.rows.length === 0) {
            throw new Error('Invalid credentials');
        }
        const user = userResult.rows[0];
        // Verify password
        const isPasswordValid = await bcryptjs_1.default.compare(password, user.password);
        if (!isPasswordValid) {
            throw new Error('Invalid credentials');
        }
        // Create user and tenant objects
        const userObj = {
            id: user.id,
            name: user.name,
            email: user.email,
            password: user.password,
            role: user.role,
            tenant_id: user.tenant_id,
            is_active: user.is_active,
            created_at: user.created_at,
            updated_at: user.updated_at,
        };
        const tenantObj = {
            id: user.tenant_id,
            name: user.tenant_name,
            slug: user.tenant_slug,
            logo: user.tenant_logo,
            is_active: user.tenant_active,
            created_at: user.created_at,
            updated_at: user.updated_at,
        };
        // Generate JWT token
        const token = (0, jwt_1.generateToken)(userObj, tenantObj);
        // Remove password from response
        const { password: _, ...userWithoutPassword } = userObj;
        return {
            token,
            user: userWithoutPassword,
            tenant: tenantObj,
        };
    }
    async register(userData, tenantId) {
        const { name, email, password, role } = userData;
        // Check if user already exists
        const existingUser = await (0, connection_1.query)('SELECT id FROM users WHERE email = $1', [email]);
        if (existingUser.rows.length > 0) {
            throw new Error('User with this email already exists');
        }
        // Hash password
        const hashedPassword = await bcryptjs_1.default.hash(password, 12);
        // Create user
        const result = await (0, connection_1.query)(`
      INSERT INTO users (name, email, password, role, tenant_id)
      VALUES ($1, $2, $3, $4, $5)
      RETURNING *
    `, [name, email, hashedPassword, role, tenantId]);
        return result.rows[0];
    }
    async changePassword(userId, currentPassword, newPassword) {
        // Get current user
        const userResult = await (0, connection_1.query)('SELECT password FROM users WHERE id = $1', [userId]);
        if (userResult.rows.length === 0) {
            throw new Error('User not found');
        }
        const user = userResult.rows[0];
        // Verify current password
        const isCurrentPasswordValid = await bcryptjs_1.default.compare(currentPassword, user.password);
        if (!isCurrentPasswordValid) {
            throw new Error('Current password is incorrect');
        }
        // Hash new password
        const hashedNewPassword = await bcryptjs_1.default.hash(newPassword, 12);
        // Update password
        await (0, connection_1.query)('UPDATE users SET password = $1, updated_at = CURRENT_TIMESTAMP WHERE id = $2', [
            hashedNewPassword,
            userId,
        ]);
    }
    async getUserById(userId) {
        const result = await (0, connection_1.query)('SELECT * FROM users WHERE id = $1', [userId]);
        return result.rows.length > 0 ? result.rows[0] : null;
    }
}
exports.AuthService = AuthService;
//# sourceMappingURL=authService.js.map