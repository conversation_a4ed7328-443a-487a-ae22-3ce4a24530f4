import { Tenant, CreateTenantRequest, PaginatedResponse } from '../types/models';
export declare class TenantService {
    createTenant(tenantData: CreateTenantRequest): Promise<Tenant>;
    getTenantById(id: string): Promise<Tenant | null>;
    getTenantBySlug(slug: string): Promise<Tenant | null>;
    getAllTenants(page?: number, limit?: number): Promise<PaginatedResponse<Tenant>>;
    updateTenant(id: string, updateData: Partial<CreateTenantRequest>): Promise<Tenant>;
    deleteTenant(id: string): Promise<void>;
    toggleTenantStatus(id: string): Promise<Tenant>;
}
//# sourceMappingURL=tenantService.d.ts.map