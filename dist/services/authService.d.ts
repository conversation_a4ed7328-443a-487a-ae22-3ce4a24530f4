import { User, LoginRequest, AuthResponse } from '../types/models';
export declare class AuthService {
    login(loginData: LoginRequest): Promise<AuthResponse>;
    register(userData: any, tenantId: string): Promise<User>;
    changePassword(userId: string, currentPassword: string, newPassword: string): Promise<void>;
    getUserById(userId: string): Promise<User | null>;
}
//# sourceMappingURL=authService.d.ts.map