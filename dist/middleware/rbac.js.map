{"version": 3, "file": "rbac.js", "sourceRoot": "", "sources": ["../../src/middleware/rbac.ts"], "names": [], "mappings": ";;;AAkBA,mCAAmC;AACtB,QAAA,eAAe,GAA+B;IACzD,KAAK,EAAE;QACL,4BAA4B;QAC5B,aAAa,EAAE,cAAc,EAAE,eAAe;QAC9C,WAAW,EAAE,YAAY,EAAE,aAAa;QACxC,eAAe,EAAE,gBAAgB,EAAE,iBAAiB;QACpD,aAAa,EAAE,cAAc,EAAE,eAAe;QAC9C,sBAAsB,EAAE,uBAAuB,EAAE,wBAAwB;QACzE,mBAAmB,EAAE,oBAAoB,EAAE,qBAAqB;QAChE,cAAc,EAAE,eAAe,EAAE,gBAAgB;QACjD,iBAAiB,EAAE,kBAAkB,EAAE,mBAAmB;QAC1D,cAAc,EAAE,eAAe,EAAE,gBAAgB;QACjD,eAAe,EAAE,gBAAgB,EAAE,iBAAiB;QACpD,cAAc,EAAE,eAAe,EAAE,gBAAgB;KAClD;IAED,UAAU,EAAE;QACV,gEAAgE;QAChE,sBAAsB,EAAE,uBAAuB;QAC/C,mBAAmB,EAAE,oBAAoB;QACzC,eAAe,EAAE,aAAa;QAC9B,iBAAiB;QACjB,cAAc,EAAE,eAAe;KAChC;IAED,QAAQ,EAAE;QACR,2CAA2C;QAC3C,eAAe,EAAE,gBAAgB,EAAE,iBAAiB;QACpD,aAAa,EAAE,cAAc,EAAE,eAAe;QAC9C,sBAAsB,EAAE,uBAAuB,EAAE,wBAAwB;QACzE,mBAAmB;QACnB,cAAc,EAAE,eAAe;QAC/B,iBAAiB,EAAE,kBAAkB;QACrC,cAAc,EAAE,eAAe;QAC/B,eAAe,EAAE,gBAAgB,EAAE,iBAAiB;QACpD,cAAc,EAAE,eAAe;KAChC;IAED,MAAM,EAAE;QACN,mBAAmB;QACnB,eAAe,EAAE,aAAa;QAC9B,sBAAsB,EAAE,mBAAmB;QAC3C,cAAc,EAAE,iBAAiB;QACjC,cAAc,EAAE,eAAe;QAC/B,cAAc;KACf;CACF,CAAC;AAEK,MAAM,aAAa,GAAG,CAAC,IAAU,EAAE,UAAsB,EAAW,EAAE;IAC3E,OAAO,uBAAe,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;AACpD,CAAC,CAAC;AAFW,QAAA,aAAa,iBAExB;AAEK,MAAM,iBAAiB,GAAG,CAAC,UAAsB,EAAE,EAAE;IAC1D,OAAO,CAAC,GAAyB,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;QACtE,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;YACd,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,yBAAyB;aACjC,CAAC,CAAC;QACL,CAAC;QAED,MAAM,QAAQ,GAAG,GAAG,CAAC,IAAI,CAAC,IAAY,CAAC;QAEvC,IAAI,CAAC,IAAA,qBAAa,EAAC,QAAQ,EAAE,UAAU,CAAC,EAAE,CAAC;YACzC,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,uCAAuC,UAAU,EAAE;aAC3D,CAAC,CAAC;QACL,CAAC;QAED,IAAI,EAAE,CAAC;IACT,CAAC,CAAC;AACJ,CAAC,CAAC;AApBW,QAAA,iBAAiB,qBAoB5B;AAEF,4FAA4F;AACrF,MAAM,wBAAwB,GAAG,CAAC,GAAyB,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;IACvG,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;QACd,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YAC1B,OAAO,EAAE,KAAK;YACd,KAAK,EAAE,yBAAyB;SACjC,CAAC,CAAC;IACL,CAAC;IAED,sDAAsD;IACtD,IAAI,GAAG,CAAC,IAAI,CAAC,IAAI,KAAK,YAAY,EAAE,CAAC;QACnC,GAAG,CAAC,KAAK,CAAC,WAAW,GAAG,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC;IAC1C,CAAC;IAED,IAAI,EAAE,CAAC;AACT,CAAC,CAAC;AAdW,QAAA,wBAAwB,4BAcnC"}