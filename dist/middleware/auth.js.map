{"version": 3, "file": "auth.js", "sourceRoot": "", "sources": ["../../src/middleware/auth.ts"], "names": [], "mappings": ";;;AACA,sCAA+E;AAMxE,MAAM,YAAY,GAAG,CAAC,GAAyB,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;IAC3F,IAAI,CAAC;QACH,MAAM,UAAU,GAAG,GAAG,CAAC,OAAO,CAAC,aAAa,CAAC;QAE7C,IAAI,CAAC,UAAU,EAAE,CAAC;YAChB,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,kCAAkC;aAC1C,CAAC,CAAC;QACL,CAAC;QAED,MAAM,KAAK,GAAG,IAAA,4BAAsB,EAAC,UAAU,CAAC,CAAC;QACjD,MAAM,OAAO,GAAG,IAAA,iBAAW,EAAC,KAAK,CAAC,CAAC;QAEnC,GAAG,CAAC,IAAI,GAAG,OAAO,CAAC;QACnB,IAAI,EAAE,CAAC;IACT,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YAC1B,OAAO,EAAE,KAAK;YACd,KAAK,EAAE,0BAA0B;SAClC,CAAC,CAAC;IACL,CAAC;AACH,CAAC,CAAC;AAtBW,QAAA,YAAY,gBAsBvB;AAEK,MAAM,SAAS,GAAG,CAAC,KAAe,EAAE,EAAE;IAC3C,OAAO,CAAC,GAAyB,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;QACtE,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;YACd,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,yBAAyB;aACjC,CAAC,CAAC;QACL,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;YACnC,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,0BAA0B;aAClC,CAAC,CAAC;QACL,CAAC;QAED,IAAI,EAAE,CAAC;IACT,CAAC,CAAC;AACJ,CAAC,CAAC;AAlBW,QAAA,SAAS,aAkBpB;AAEF,kCAAkC;AACrB,QAAA,SAAS,GAAG,IAAA,iBAAS,EAAC,CAAC,OAAO,CAAC,CAAC,CAAC;AACjC,QAAA,eAAe,GAAG,IAAA,iBAAS,EAAC,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC,CAAC;AACnD,QAAA,iBAAiB,GAAG,IAAA,iBAAS,EAAC,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC,CAAC;AACvD,QAAA,QAAQ,GAAG,IAAA,iBAAS,EAAC,CAAC,OAAO,EAAE,UAAU,EAAE,YAAY,EAAE,QAAQ,CAAC,CAAC,CAAC"}