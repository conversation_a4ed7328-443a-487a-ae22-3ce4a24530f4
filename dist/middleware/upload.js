"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getFileUrl = exports.deleteFile = exports.handleUploadError = exports.uploadMultiple = exports.uploadLogo = exports.uploadInvoicePDF = exports.uploadPhoto = void 0;
const multer_1 = __importDefault(require("multer"));
const path_1 = __importDefault(require("path"));
const uuid_1 = require("uuid");
const fs_1 = __importDefault(require("fs"));
// Ensure upload directory exists
const uploadDir = process.env.UPLOAD_PATH || './uploads';
if (!fs_1.default.existsSync(uploadDir)) {
    fs_1.default.mkdirSync(uploadDir, { recursive: true });
}
// Create subdirectories for different file types
const subdirs = ['photos', 'invoices', 'logos'];
subdirs.forEach(subdir => {
    const fullPath = path_1.default.join(uploadDir, subdir);
    if (!fs_1.default.existsSync(fullPath)) {
        fs_1.default.mkdirSync(fullPath, { recursive: true });
    }
});
// Configure storage
const storage = multer_1.default.diskStorage({
    destination: (req, file, cb) => {
        let subdir = 'photos'; // default
        if (file.fieldname === 'invoice_pdf') {
            subdir = 'invoices';
        }
        else if (file.fieldname === 'logo') {
            subdir = 'logos';
        }
        else if (file.fieldname === 'photo') {
            subdir = 'photos';
        }
        cb(null, path_1.default.join(uploadDir, subdir));
    },
    filename: (req, file, cb) => {
        // Generate unique filename with original extension
        const uniqueName = `${(0, uuid_1.v4)()}${path_1.default.extname(file.originalname)}`;
        cb(null, uniqueName);
    }
});
// File filter function
const fileFilter = (req, file, cb) => {
    // Define allowed file types for different fields
    const allowedTypes = {
        photo: ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'],
        invoice_pdf: ['application/pdf'],
        logo: ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/svg+xml']
    };
    const fieldAllowedTypes = allowedTypes[file.fieldname] || allowedTypes.photo;
    if (fieldAllowedTypes.includes(file.mimetype)) {
        cb(null, true);
    }
    else {
        cb(new Error(`Invalid file type for ${file.fieldname}. Allowed types: ${fieldAllowedTypes.join(', ')}`));
    }
};
// Configure multer
const upload = (0, multer_1.default)({
    storage,
    fileFilter,
    limits: {
        fileSize: parseInt(process.env.MAX_FILE_SIZE || '10485760'), // 10MB default
        files: 5 // Maximum 5 files per request
    }
});
// Middleware for different upload scenarios
exports.uploadPhoto = upload.single('photo');
exports.uploadInvoicePDF = upload.single('invoice_pdf');
exports.uploadLogo = upload.single('logo');
exports.uploadMultiple = upload.array('files', 5);
// Error handling middleware for multer
const handleUploadError = (error, req, res, next) => {
    if (error instanceof multer_1.default.MulterError) {
        if (error.code === 'LIMIT_FILE_SIZE') {
            return res.status(400).json({
                success: false,
                error: 'File too large. Maximum size is 10MB.'
            });
        }
        if (error.code === 'LIMIT_FILE_COUNT') {
            return res.status(400).json({
                success: false,
                error: 'Too many files. Maximum 5 files allowed.'
            });
        }
        if (error.code === 'LIMIT_UNEXPECTED_FILE') {
            return res.status(400).json({
                success: false,
                error: 'Unexpected file field.'
            });
        }
    }
    if (error.message.includes('Invalid file type')) {
        return res.status(400).json({
            success: false,
            error: error.message
        });
    }
    next(error);
};
exports.handleUploadError = handleUploadError;
// Utility function to delete uploaded file
const deleteFile = (filePath) => {
    try {
        if (fs_1.default.existsSync(filePath)) {
            fs_1.default.unlinkSync(filePath);
        }
    }
    catch (error) {
        console.error('Error deleting file:', error);
    }
};
exports.deleteFile = deleteFile;
// Utility function to get file URL
const getFileUrl = (filename, type = 'photos') => {
    return `/uploads/${type}/${filename}`;
};
exports.getFileUrl = getFileUrl;
//# sourceMappingURL=upload.js.map