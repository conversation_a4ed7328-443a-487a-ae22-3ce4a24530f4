"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.technicianResourceFilter = exports.requirePermission = exports.hasPermission = exports.rolePermissions = void 0;
// Define permissions for each role
exports.rolePermissions = {
    admin: [
        // Full access to everything
        'tenant:read', 'tenant:write', 'tenant:delete',
        'user:read', 'user:write', 'user:delete',
        'customer:read', 'customer:write', 'customer:delete',
        'device:read', 'device:write', 'device:delete',
        'service_request:read', 'service_request:write', 'service_request:delete',
        'service_note:read', 'service_note:write', 'service_note:delete',
        'invoice:read', 'invoice:write', 'invoice:delete',
        'stock_item:read', 'stock_item:write', 'stock_item:delete',
        'payment:read', 'payment:write', 'payment:delete',
        'reminder:read', 'reminder:write', 'reminder:delete',
        'message:read', 'message:write', 'message:delete',
    ],
    technician: [
        // Can only work with assigned service requests and related data
        'service_request:read', 'service_request:write',
        'service_note:read', 'service_note:write',
        'customer:read', 'device:read',
        'stock_item:read',
        'message:read', 'message:write',
    ],
    operator: [
        // CRUD on customers, devices, and requests
        'customer:read', 'customer:write', 'customer:delete',
        'device:read', 'device:write', 'device:delete',
        'service_request:read', 'service_request:write', 'service_request:delete',
        'service_note:read',
        'invoice:read', 'invoice:write',
        'stock_item:read', 'stock_item:write',
        'payment:read', 'payment:write',
        'reminder:read', 'reminder:write', 'reminder:delete',
        'message:read', 'message:write',
    ],
    viewer: [
        // Read-only access
        'customer:read', 'device:read',
        'service_request:read', 'service_note:read',
        'invoice:read', 'stock_item:read',
        'payment:read', 'reminder:read',
        'message:read',
    ],
};
const hasPermission = (role, permission) => {
    return exports.rolePermissions[role].includes(permission);
};
exports.hasPermission = hasPermission;
const requirePermission = (permission) => {
    return (req, res, next) => {
        if (!req.user) {
            return res.status(401).json({
                success: false,
                error: 'Authentication required'
            });
        }
        const userRole = req.user.role;
        if (!(0, exports.hasPermission)(userRole, permission)) {
            return res.status(403).json({
                success: false,
                error: `Insufficient permissions. Required: ${permission}`
            });
        }
        next();
    };
};
exports.requirePermission = requirePermission;
// Special middleware for technicians - they can only access their assigned service requests
const technicianResourceFilter = (req, res, next) => {
    if (!req.user) {
        return res.status(401).json({
            success: false,
            error: 'Authentication required'
        });
    }
    // If user is a technician, add filter for assigned_to
    if (req.user.role === 'technician') {
        req.query.assigned_to = req.user.userId;
    }
    next();
};
exports.technicianResourceFilter = technicianResourceFilter;
//# sourceMappingURL=rbac.js.map