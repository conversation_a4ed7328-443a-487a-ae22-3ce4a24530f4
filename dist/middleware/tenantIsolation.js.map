{"version": 3, "file": "tenantIsolation.js", "sourceRoot": "", "sources": ["../../src/middleware/tenantIsolation.ts"], "names": [], "mappings": ";;;AAOO,MAAM,eAAe,GAAG,CAAC,GAAkB,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;IACvF,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;QACd,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YAC1B,OAAO,EAAE,KAAK;YACd,KAAK,EAAE,8CAA8C;SACtD,CAAC,CAAC;IACL,CAAC;IAED,2CAA2C;IAC3C,GAAG,CAAC,QAAQ,GAAG,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC;IACjC,IAAI,EAAE,CAAC;AACT,CAAC,CAAC;AAXW,QAAA,eAAe,mBAW1B;AAEF,iEAAiE;AACjE,MAAa,gBAAgB;IAG3B,YAAY,QAAgB;QAC1B,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;IAC3B,CAAC;IAED,oCAAoC;IACpC,eAAe,CAAC,SAAiB,EAAE,SAAgB,EAAE;QACnD,MAAM,eAAe,GAAG,MAAM,CAAC,MAAM,KAAK,CAAC;YACzC,CAAC,CAAC,sBAAsB;YACxB,CAAC,CAAC,mBAAmB,GAAG,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QAE9C,MAAM,KAAK,GAAG,SAAS,CAAC,QAAQ,CAAC,OAAO,CAAC;YACvC,CAAC,CAAC,SAAS,GAAG,GAAG,GAAG,eAAe,CAAC,OAAO,CAAC,OAAO,EAAE,KAAK,CAAC;YAC3D,CAAC,CAAC,SAAS,GAAG,GAAG,GAAG,eAAe,CAAC;QAEtC,OAAO;YACL,KAAK;YACL,MAAM,EAAE,CAAC,GAAG,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC;SACnC,CAAC;IACJ,CAAC;IAED,kDAAkD;IAClD,iBAAiB,CAAC,OAAiB,EAAE,MAAa;QAChD,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;YACnC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAC1B,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC7B,CAAC;QACD,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC;IAC7B,CAAC;IAED,sCAAsC;IACtC,oBAAoB,CAAC,KAAa,EAAE,aAAqB,CAAC;QACxD,OAAO,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,IAAI,UAAU,GAAG,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAClF,CAAC;CACF;AApCD,4CAoCC;AAED,wDAAwD;AACjD,MAAM,iBAAiB,GAAG,CAAC,GAAkB,EAAoB,EAAE;IACxE,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC;QAClB,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;IACpD,CAAC;IACD,OAAO,IAAI,gBAAgB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;AAC5C,CAAC,CAAC;AALW,QAAA,iBAAiB,qBAK5B"}