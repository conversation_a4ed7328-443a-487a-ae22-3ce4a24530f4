"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createTenantQuery = exports.TenantAwareQuery = exports.tenantIsolation = void 0;
const tenantIsolation = (req, res, next) => {
    if (!req.user) {
        return res.status(401).json({
            success: false,
            error: 'Authentication required for tenant isolation'
        });
    }
    // Add tenant ID to request for easy access
    req.tenantId = req.user.tenantId;
    next();
};
exports.tenantIsolation = tenantIsolation;
// Database query helper that automatically adds tenant filtering
class TenantAwareQuery {
    constructor(tenantId) {
        this.tenantId = tenantId;
    }
    // Add tenant filter to WHERE clause
    addTenantFilter(baseQuery, params = []) {
        const tenantCondition = params.length === 0
            ? 'WHERE tenant_id = $1'
            : 'AND tenant_id = $' + (params.length + 1);
        const query = baseQuery.includes('WHERE')
            ? baseQuery + ' ' + tenantCondition.replace('WHERE', 'AND')
            : baseQuery + ' ' + tenantCondition;
        return {
            query,
            params: [...params, this.tenantId]
        };
    }
    // For INSERT queries, automatically add tenant_id
    addTenantToInsert(columns, values) {
        if (!columns.includes('tenant_id')) {
            columns.push('tenant_id');
            values.push(this.tenantId);
        }
        return { columns, values };
    }
    // Generate parameterized placeholders
    generatePlaceholders(count, startIndex = 1) {
        return Array.from({ length: count }, (_, i) => `$${startIndex + i}`).join(', ');
    }
}
exports.TenantAwareQuery = TenantAwareQuery;
// Helper function to create tenant-aware query instance
const createTenantQuery = (req) => {
    if (!req.tenantId) {
        throw new Error('Tenant ID not found in request');
    }
    return new TenantAwareQuery(req.tenantId);
};
exports.createTenantQuery = createTenantQuery;
//# sourceMappingURL=tenantIsolation.js.map