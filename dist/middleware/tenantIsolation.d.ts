import { Response, NextFunction } from 'express';
import { AuthenticatedRequest } from './auth';
export interface TenantRequest extends AuthenticatedRequest {
    tenantId?: string;
}
export declare const tenantIsolation: (req: TenantRequest, res: Response, next: NextFunction) => Response<any, Record<string, any>> | undefined;
export declare class TenantAwareQuery {
    private tenantId;
    constructor(tenantId: string);
    addTenantFilter(baseQuery: string, params?: any[]): {
        query: string;
        params: any[];
    };
    addTenantToInsert(columns: string[], values: any[]): {
        columns: string[];
        values: any[];
    };
    generatePlaceholders(count: number, startIndex?: number): string;
}
export declare const createTenantQuery: (req: TenantRequest) => TenantAwareQuery;
//# sourceMappingURL=tenantIsolation.d.ts.map