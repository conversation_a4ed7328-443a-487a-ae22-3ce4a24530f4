"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.allRoles = exports.adminOrTechnician = exports.adminOrOperator = exports.adminOnly = exports.authorize = exports.authenticate = void 0;
const jwt_1 = require("../utils/jwt");
const authenticate = (req, res, next) => {
    try {
        const authHeader = req.headers.authorization;
        if (!authHeader) {
            return res.status(401).json({
                success: false,
                error: 'Authorization header is required'
            });
        }
        const token = (0, jwt_1.extractTokenFromHeader)(authHeader);
        const payload = (0, jwt_1.verifyToken)(token);
        req.user = payload;
        next();
    }
    catch (error) {
        return res.status(401).json({
            success: false,
            error: 'Invalid or expired token'
        });
    }
};
exports.authenticate = authenticate;
const authorize = (roles) => {
    return (req, res, next) => {
        if (!req.user) {
            return res.status(401).json({
                success: false,
                error: 'Authentication required'
            });
        }
        if (!roles.includes(req.user.role)) {
            return res.status(403).json({
                success: false,
                error: 'Insufficient permissions'
            });
        }
        next();
    };
};
exports.authorize = authorize;
// Role-based middleware shortcuts
exports.adminOnly = (0, exports.authorize)(['admin']);
exports.adminOrOperator = (0, exports.authorize)(['admin', 'operator']);
exports.adminOrTechnician = (0, exports.authorize)(['admin', 'technician']);
exports.allRoles = (0, exports.authorize)(['admin', 'operator', 'technician', 'viewer']);
//# sourceMappingURL=auth.js.map