import { Response, NextFunction } from 'express';
import { AuthenticatedRequest } from './auth';
export type Permission = 'tenant:read' | 'tenant:write' | 'tenant:delete' | 'user:read' | 'user:write' | 'user:delete' | 'customer:read' | 'customer:write' | 'customer:delete' | 'device:read' | 'device:write' | 'device:delete' | 'service_request:read' | 'service_request:write' | 'service_request:delete' | 'service_note:read' | 'service_note:write' | 'service_note:delete' | 'invoice:read' | 'invoice:write' | 'invoice:delete' | 'stock_item:read' | 'stock_item:write' | 'stock_item:delete' | 'payment:read' | 'payment:write' | 'payment:delete' | 'reminder:read' | 'reminder:write' | 'reminder:delete' | 'message:read' | 'message:write' | 'message:delete';
export type Role = 'admin' | 'technician' | 'operator' | 'viewer';
export declare const rolePermissions: Record<Role, Permission[]>;
export declare const hasPermission: (role: Role, permission: Permission) => boolean;
export declare const requirePermission: (permission: Permission) => (req: AuthenticatedRequest, res: Response, next: NextFunction) => Response<any, Record<string, any>> | undefined;
export declare const technicianResourceFilter: (req: AuthenticatedRequest, res: Response, next: NextFunction) => Response<any, Record<string, any>> | undefined;
//# sourceMappingURL=rbac.d.ts.map