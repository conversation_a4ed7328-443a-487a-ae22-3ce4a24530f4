export interface Tenant {
    id: string;
    name: string;
    slug: string;
    logo?: string;
    is_active: boolean;
    deleted_at?: Date;
    created_at: Date;
    updated_at: Date;
}
export interface User {
    id: string;
    name: string;
    email: string;
    password: string;
    role: 'admin' | 'technician' | 'operator' | 'viewer';
    tenant_id: string;
    is_active: boolean;
    deleted_at?: Date;
    last_login?: Date;
    created_at: Date;
    updated_at: Date;
}
export interface Customer {
    id: string;
    name: string;
    phone: string;
    email?: string;
    address?: string;
    tenant_id: string;
    created_by: string;
    created_at: Date;
    updated_at: Date;
}
export interface Device {
    id: string;
    customer_id: string;
    brand: string;
    model: string;
    serial_number?: string;
    warranty_expiry?: Date;
    tenant_id: string;
    created_at: Date;
    updated_at: Date;
}
export interface ServiceRequest {
    id: string;
    device_id: string;
    status: 'Pending' | 'Assigned' | 'OnTheWay' | 'InRepair' | 'Completed';
    description: string;
    assigned_to?: string;
    request_date: Date;
    estimated_completion?: Date;
    tenant_id: string;
    created_at: Date;
    updated_at: Date;
}
export interface ServiceNote {
    id: string;
    service_request_id: string;
    note: string;
    photo?: string;
    created_by: string;
    tenant_id: string;
    created_at: Date;
}
export interface Invoice {
    id: string;
    service_request_id: string;
    amount: number;
    paid: boolean;
    issue_date: Date;
    due_date?: Date;
    invoice_pdf?: string;
    tenant_id: string;
    created_at: Date;
    updated_at: Date;
}
export interface StockItem {
    id: string;
    name: string;
    code?: string;
    quantity: number;
    minimum_threshold?: number;
    price?: number;
    tenant_id: string;
    created_at: Date;
    updated_at: Date;
}
export interface Payment {
    id: string;
    invoice_id: string;
    amount: number;
    payment_method: 'Cash' | 'Card' | 'Online';
    paid_by?: string;
    paid_at: Date;
    tenant_id: string;
}
export interface Reminder {
    id: string;
    customer_id: string;
    title: string;
    remind_date: Date;
    is_sent: boolean;
    tenant_id: string;
    created_at: Date;
    updated_at: Date;
}
export interface Message {
    id: string;
    from_user_id: string;
    to_user_id: string;
    content: string;
    read: boolean;
    tenant_id: string;
    created_at: Date;
}
export interface CreateTenantRequest {
    name: string;
    slug: string;
    logo?: string;
}
export interface CreateUserRequest {
    name: string;
    email: string;
    password: string;
    role: 'admin' | 'technician' | 'operator' | 'viewer';
}
export interface LoginRequest {
    email: string;
    password: string;
}
export interface AuthResponse {
    token: string;
    user: Omit<User, 'password'>;
    tenant: Tenant;
}
export interface PaginatedResponse<T> {
    data: T[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
}
export interface ApiResponse<T = any> {
    success: boolean;
    data?: T;
    message?: string;
    error?: string;
}
//# sourceMappingURL=models.d.ts.map