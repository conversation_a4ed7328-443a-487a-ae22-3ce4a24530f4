"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const auth_1 = require("../middleware/auth");
const tenantIsolation_1 = require("../middleware/tenantIsolation");
const rbac_1 = require("../middleware/rbac");
const router = (0, express_1.Router)();
router.use(auth_1.authenticate);
router.use(tenantIsolation_1.tenantIsolation);
router.post('/', (0, rbac_1.requirePermission)('invoice:write'), (req, res) => {
    res.json({ success: true, message: 'Create invoice - Not implemented yet' });
});
router.get('/', (0, rbac_1.requirePermission)('invoice:read'), (req, res) => {
    res.json({ success: true, message: 'Get all invoices - Not implemented yet' });
});
router.get('/:id', (0, rbac_1.requirePermission)('invoice:read'), (req, res) => {
    res.json({ success: true, message: 'Get invoice by ID - Not implemented yet' });
});
router.put('/:id', (0, rbac_1.requirePermission)('invoice:write'), (req, res) => {
    res.json({ success: true, message: 'Update invoice - Not implemented yet' });
});
router.delete('/:id', (0, rbac_1.requirePermission)('invoice:delete'), (req, res) => {
    res.json({ success: true, message: 'Delete invoice - Not implemented yet' });
});
exports.default = router;
//# sourceMappingURL=invoice.js.map