"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const auth_1 = require("../middleware/auth");
const tenantIsolation_1 = require("../middleware/tenantIsolation");
const rbac_1 = require("../middleware/rbac");
const router = (0, express_1.Router)();
// Apply authentication and tenant isolation to all routes
router.use(auth_1.authenticate);
router.use(tenantIsolation_1.tenantIsolation);
/**
 * @swagger
 * components:
 *   schemas:
 *     ServiceRequest:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           format: uuid
 *         device_id:
 *           type: string
 *           format: uuid
 *         status:
 *           type: string
 *           enum: [Pending, Assigned, OnTheWay, InRepair, Completed]
 *         description:
 *           type: string
 *         assigned_to:
 *           type: string
 *           format: uuid
 *         request_date:
 *           type: string
 *           format: date
 *         estimated_completion:
 *           type: string
 *           format: date
 *         tenant_id:
 *           type: string
 *           format: uuid
 *         created_at:
 *           type: string
 *           format: date-time
 *         updated_at:
 *           type: string
 *           format: date-time
 */
router.post('/', (0, rbac_1.requirePermission)('service_request:write'), (req, res) => {
    res.json({ success: true, message: 'Create service request - Not implemented yet' });
});
router.get('/', (0, rbac_1.requirePermission)('service_request:read'), rbac_1.technicianResourceFilter, (req, res) => {
    res.json({ success: true, message: 'Get all service requests - Not implemented yet' });
});
router.get('/:id', (0, rbac_1.requirePermission)('service_request:read'), (req, res) => {
    res.json({ success: true, message: 'Get service request by ID - Not implemented yet' });
});
router.put('/:id', (0, rbac_1.requirePermission)('service_request:write'), (req, res) => {
    res.json({ success: true, message: 'Update service request - Not implemented yet' });
});
router.delete('/:id', (0, rbac_1.requirePermission)('service_request:delete'), (req, res) => {
    res.json({ success: true, message: 'Delete service request - Not implemented yet' });
});
exports.default = router;
//# sourceMappingURL=serviceRequest.js.map