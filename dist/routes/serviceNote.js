"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const auth_1 = require("../middleware/auth");
const tenantIsolation_1 = require("../middleware/tenantIsolation");
const rbac_1 = require("../middleware/rbac");
const router = (0, express_1.Router)();
router.use(auth_1.authenticate);
router.use(tenantIsolation_1.tenantIsolation);
router.post('/', (0, rbac_1.requirePermission)('service_note:write'), (req, res) => {
    res.json({ success: true, message: 'Create service note - Not implemented yet' });
});
router.get('/', (0, rbac_1.requirePermission)('service_note:read'), (req, res) => {
    res.json({ success: true, message: 'Get all service notes - Not implemented yet' });
});
router.get('/:id', (0, rbac_1.requirePermission)('service_note:read'), (req, res) => {
    res.json({ success: true, message: 'Get service note by ID - Not implemented yet' });
});
router.put('/:id', (0, rbac_1.requirePermission)('service_note:write'), (req, res) => {
    res.json({ success: true, message: 'Update service note - Not implemented yet' });
});
router.delete('/:id', (0, rbac_1.requirePermission)('service_note:delete'), (req, res) => {
    res.json({ success: true, message: 'Delete service note - Not implemented yet' });
});
exports.default = router;
//# sourceMappingURL=serviceNote.js.map