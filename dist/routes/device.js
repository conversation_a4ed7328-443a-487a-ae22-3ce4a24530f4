"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const auth_1 = require("../middleware/auth");
const tenantIsolation_1 = require("../middleware/tenantIsolation");
const rbac_1 = require("../middleware/rbac");
const router = (0, express_1.Router)();
// Apply authentication and tenant isolation to all routes
router.use(auth_1.authenticate);
router.use(tenantIsolation_1.tenantIsolation);
/**
 * @swagger
 * components:
 *   schemas:
 *     Device:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           format: uuid
 *         customer_id:
 *           type: string
 *           format: uuid
 *         brand:
 *           type: string
 *         model:
 *           type: string
 *         serial_number:
 *           type: string
 *         warranty_expiry:
 *           type: string
 *           format: date
 *         tenant_id:
 *           type: string
 *           format: uuid
 *         created_at:
 *           type: string
 *           format: date-time
 *         updated_at:
 *           type: string
 *           format: date-time
 */
router.post('/', (0, rbac_1.requirePermission)('device:write'), (req, res) => {
    res.json({ success: true, message: 'Create device - Not implemented yet' });
});
router.get('/', (0, rbac_1.requirePermission)('device:read'), (req, res) => {
    res.json({ success: true, message: 'Get all devices - Not implemented yet' });
});
router.get('/:id', (0, rbac_1.requirePermission)('device:read'), (req, res) => {
    res.json({ success: true, message: 'Get device by ID - Not implemented yet' });
});
router.put('/:id', (0, rbac_1.requirePermission)('device:write'), (req, res) => {
    res.json({ success: true, message: 'Update device - Not implemented yet' });
});
router.delete('/:id', (0, rbac_1.requirePermission)('device:delete'), (req, res) => {
    res.json({ success: true, message: 'Delete device - Not implemented yet' });
});
exports.default = router;
//# sourceMappingURL=device.js.map