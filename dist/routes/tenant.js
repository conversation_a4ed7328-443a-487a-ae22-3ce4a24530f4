"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const auth_1 = require("../middleware/auth");
const router = (0, express_1.Router)();
// Apply authentication to all routes
router.use(auth_1.authenticate);
/**
 * @swagger
 * components:
 *   schemas:
 *     Tenant:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           format: uuid
 *         name:
 *           type: string
 *         slug:
 *           type: string
 *         logo:
 *           type: string
 *         is_active:
 *           type: boolean
 *         created_at:
 *           type: string
 *           format: date-time
 *         updated_at:
 *           type: string
 *           format: date-time
 */
// Placeholder routes - only admins can manage tenants
router.get('/', auth_1.adminOnly, (req, res) => {
    res.json({ success: true, message: 'Get all tenants - Not implemented yet' });
});
router.post('/', auth_1.adminOnly, (req, res) => {
    res.json({ success: true, message: 'Create tenant - Not implemented yet' });
});
router.get('/:id', auth_1.adminOnly, (req, res) => {
    res.json({ success: true, message: 'Get tenant by ID - Not implemented yet' });
});
router.put('/:id', auth_1.adminOnly, (req, res) => {
    res.json({ success: true, message: 'Update tenant - Not implemented yet' });
});
router.delete('/:id', auth_1.adminOnly, (req, res) => {
    res.json({ success: true, message: 'Delete tenant - Not implemented yet' });
});
exports.default = router;
//# sourceMappingURL=tenant.js.map