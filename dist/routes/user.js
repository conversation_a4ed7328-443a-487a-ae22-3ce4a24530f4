"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const auth_1 = require("../middleware/auth");
const tenantIsolation_1 = require("../middleware/tenantIsolation");
const rbac_1 = require("../middleware/rbac");
const router = (0, express_1.Router)();
// Apply authentication and tenant isolation to all routes
router.use(auth_1.authenticate);
router.use(tenantIsolation_1.tenantIsolation);
/**
 * @swagger
 * components:
 *   schemas:
 *     User:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           format: uuid
 *         name:
 *           type: string
 *         email:
 *           type: string
 *           format: email
 *         role:
 *           type: string
 *           enum: [admin, technician, operator, viewer]
 *         tenant_id:
 *           type: string
 *           format: uuid
 *         is_active:
 *           type: boolean
 *         created_at:
 *           type: string
 *           format: date-time
 *         updated_at:
 *           type: string
 *           format: date-time
 */
router.get('/', (0, rbac_1.requirePermission)('user:read'), (req, res) => {
    res.json({ success: true, message: 'Get all users - Not implemented yet' });
});
router.get('/:id', (0, rbac_1.requirePermission)('user:read'), (req, res) => {
    res.json({ success: true, message: 'Get user by ID - Not implemented yet' });
});
router.put('/:id', (0, rbac_1.requirePermission)('user:write'), (req, res) => {
    res.json({ success: true, message: 'Update user - Not implemented yet' });
});
router.delete('/:id', (0, rbac_1.requirePermission)('user:delete'), (req, res) => {
    res.json({ success: true, message: 'Delete user - Not implemented yet' });
});
exports.default = router;
//# sourceMappingURL=user.js.map