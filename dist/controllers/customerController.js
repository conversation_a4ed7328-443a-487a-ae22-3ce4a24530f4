"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getCustomerDevices = exports.deleteCustomer = exports.updateCustomer = exports.getCustomerById = exports.getCustomers = exports.createCustomer = exports.updateCustomerValidation = exports.createCustomerValidation = void 0;
const express_validator_1 = require("express-validator");
const customerService_1 = require("../services/customerService");
const errorHandler_1 = require("../middleware/errorHandler");
const customerService = new customerService_1.CustomerService();
exports.createCustomerValidation = [
    (0, express_validator_1.body)('name').notEmpty().withMessage('Name is required'),
    (0, express_validator_1.body)('phone').notEmpty().withMessage('Phone is required'),
    (0, express_validator_1.body)('email').optional().isEmail().withMessage('Valid email is required'),
    (0, express_validator_1.body)('address').optional().isString(),
];
exports.updateCustomerValidation = [
    (0, express_validator_1.body)('name').optional().notEmpty().withMessage('Name cannot be empty'),
    (0, express_validator_1.body)('phone').optional().notEmpty().withMessage('Phone cannot be empty'),
    (0, express_validator_1.body)('email').optional().isEmail().withMessage('Valid email is required'),
    (0, express_validator_1.body)('address').optional().isString(),
];
exports.createCustomer = (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const errors = (0, express_validator_1.validationResult)(req);
    if (!errors.isEmpty()) {
        return res.status(400).json({
            success: false,
            error: 'Validation failed',
            details: errors.array(),
        });
    }
    try {
        const customer = await customerService.createCustomer(req.body, req.tenantId, req.user.userId);
        res.status(201).json({
            success: true,
            data: customer,
            message: 'Customer created successfully',
        });
    }
    catch (error) {
        res.status(400).json({
            success: false,
            error: error.message,
        });
    }
});
exports.getCustomers = (0, errorHandler_1.asyncHandler)(async (req, res) => {
    try {
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 10;
        const search = req.query.search;
        const result = await customerService.getAllCustomers(req.tenantId, page, limit, search);
        res.json({
            success: true,
            data: result,
        });
    }
    catch (error) {
        res.status(500).json({
            success: false,
            error: error.message,
        });
    }
});
exports.getCustomerById = (0, errorHandler_1.asyncHandler)(async (req, res) => {
    try {
        const customer = await customerService.getCustomerById(req.params.id, req.tenantId);
        if (!customer) {
            return res.status(404).json({
                success: false,
                error: 'Customer not found',
            });
        }
        res.json({
            success: true,
            data: customer,
        });
    }
    catch (error) {
        res.status(500).json({
            success: false,
            error: error.message,
        });
    }
});
exports.updateCustomer = (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const errors = (0, express_validator_1.validationResult)(req);
    if (!errors.isEmpty()) {
        return res.status(400).json({
            success: false,
            error: 'Validation failed',
            details: errors.array(),
        });
    }
    try {
        const customer = await customerService.updateCustomer(req.params.id, req.body, req.tenantId);
        res.json({
            success: true,
            data: customer,
            message: 'Customer updated successfully',
        });
    }
    catch (error) {
        res.status(400).json({
            success: false,
            error: error.message,
        });
    }
});
exports.deleteCustomer = (0, errorHandler_1.asyncHandler)(async (req, res) => {
    try {
        await customerService.deleteCustomer(req.params.id, req.tenantId);
        res.json({
            success: true,
            message: 'Customer deleted successfully',
        });
    }
    catch (error) {
        res.status(400).json({
            success: false,
            error: error.message,
        });
    }
});
exports.getCustomerDevices = (0, errorHandler_1.asyncHandler)(async (req, res) => {
    try {
        const devices = await customerService.getCustomerDevices(req.params.id, req.tenantId);
        res.json({
            success: true,
            data: devices,
        });
    }
    catch (error) {
        res.status(500).json({
            success: false,
            error: error.message,
        });
    }
});
//# sourceMappingURL=customerController.js.map