import { Request, Response } from 'express';
export declare const loginValidation: import("express-validator").ValidationChain[];
export declare const registerValidation: import("express-validator").ValidationChain[];
export declare const changePasswordValidation: import("express-validator").ValidationChain[];
export declare const login: (req: Request, res: Response, next: import("express").NextFunction) => void;
export declare const register: (req: Request, res: Response, next: import("express").NextFunction) => void;
export declare const changePassword: (req: Request, res: Response, next: import("express").NextFunction) => void;
export declare const getProfile: (req: Request, res: Response, next: import("express").NextFunction) => void;
//# sourceMappingURL=authController.d.ts.map