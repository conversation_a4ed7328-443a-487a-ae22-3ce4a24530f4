"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getProfile = exports.changePassword = exports.register = exports.login = exports.changePasswordValidation = exports.registerValidation = exports.loginValidation = void 0;
const express_validator_1 = require("express-validator");
const authService_1 = require("../services/authService");
const errorHandler_1 = require("../middleware/errorHandler");
const authService = new authService_1.AuthService();
exports.loginValidation = [
    (0, express_validator_1.body)('email').isEmail().withMessage('Valid email is required'),
    (0, express_validator_1.body)('password').isLength({ min: 6 }).withMessage('Password must be at least 6 characters'),
];
exports.registerValidation = [
    (0, express_validator_1.body)('name').notEmpty().withMessage('Name is required'),
    (0, express_validator_1.body)('email').isEmail().withMessage('Valid email is required'),
    (0, express_validator_1.body)('password').isLength({ min: 6 }).withMessage('Password must be at least 6 characters'),
    (0, express_validator_1.body)('role').isIn(['admin', 'technician', 'operator', 'viewer']).withMessage('Invalid role'),
];
exports.changePasswordValidation = [
    (0, express_validator_1.body)('currentPassword').notEmpty().withMessage('Current password is required'),
    (0, express_validator_1.body)('newPassword').isLength({ min: 6 }).withMessage('New password must be at least 6 characters'),
];
exports.login = (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const errors = (0, express_validator_1.validationResult)(req);
    if (!errors.isEmpty()) {
        return res.status(400).json({
            success: false,
            error: 'Validation failed',
            details: errors.array(),
        });
    }
    try {
        const authResponse = await authService.login(req.body);
        res.json({
            success: true,
            data: authResponse,
            message: 'Login successful',
        });
    }
    catch (error) {
        res.status(401).json({
            success: false,
            error: error.message,
        });
    }
});
exports.register = (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const errors = (0, express_validator_1.validationResult)(req);
    if (!errors.isEmpty()) {
        return res.status(400).json({
            success: false,
            error: 'Validation failed',
            details: errors.array(),
        });
    }
    // Only admins can register new users
    if (req.user?.role !== 'admin') {
        return res.status(403).json({
            success: false,
            error: 'Only admins can register new users',
        });
    }
    try {
        const user = await authService.register(req.body, req.user.tenantId);
        // Remove password from response
        const { password, ...userWithoutPassword } = user;
        res.status(201).json({
            success: true,
            data: userWithoutPassword,
            message: 'User registered successfully',
        });
    }
    catch (error) {
        res.status(400).json({
            success: false,
            error: error.message,
        });
    }
});
exports.changePassword = (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const errors = (0, express_validator_1.validationResult)(req);
    if (!errors.isEmpty()) {
        return res.status(400).json({
            success: false,
            error: 'Validation failed',
            details: errors.array(),
        });
    }
    try {
        await authService.changePassword(req.user.userId, req.body.currentPassword, req.body.newPassword);
        res.json({
            success: true,
            message: 'Password changed successfully',
        });
    }
    catch (error) {
        res.status(400).json({
            success: false,
            error: error.message,
        });
    }
});
exports.getProfile = (0, errorHandler_1.asyncHandler)(async (req, res) => {
    try {
        const user = await authService.getUserById(req.user.userId);
        if (!user) {
            return res.status(404).json({
                success: false,
                error: 'User not found',
            });
        }
        // Remove password from response
        const { password, ...userWithoutPassword } = user;
        res.json({
            success: true,
            data: userWithoutPassword,
        });
    }
    catch (error) {
        res.status(500).json({
            success: false,
            error: error.message,
        });
    }
});
//# sourceMappingURL=authController.js.map