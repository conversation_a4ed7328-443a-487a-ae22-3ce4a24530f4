import { Response } from 'express';
export declare const createCustomerValidation: import("express-validator").ValidationChain[];
export declare const updateCustomerValidation: import("express-validator").ValidationChain[];
export declare const createCustomer: (req: import("express").Request, res: Response, next: import("express").NextFunction) => void;
export declare const getCustomers: (req: import("express").Request, res: Response, next: import("express").NextFunction) => void;
export declare const getCustomerById: (req: import("express").Request, res: Response, next: import("express").NextFunction) => void;
export declare const updateCustomer: (req: import("express").Request, res: Response, next: import("express").NextFunction) => void;
export declare const deleteCustomer: (req: import("express").Request, res: Response, next: import("express").NextFunction) => void;
export declare const getCustomerDevices: (req: import("express").Request, res: Response, next: import("express").NextFunction) => void;
//# sourceMappingURL=customerController.d.ts.map